import re
import asyncio
from typing import Dict, Optional

from database import pricing, db
from .models import PriceQuery, QueryState

class ChatManager:
    """Manages the conversation state for price queries."""

    def __init__(self):
        self.sessions: Dict[str, PriceQuery] = {}

    def get_or_create_session(self, session_id: str) -> PriceQuery:
        """Get an existing session or create a new one."""
        if session_id not in self.sessions:
            self.sessions[session_id] = PriceQuery(session_id=session_id)
        return self.sessions[session_id]

    def process_message(self, session_id: str, message: str) -> str:
        """Process an incoming message and return a response."""
        query = self.get_or_create_session(session_id)

        # If query is already complete, we could start a new one or just respond.
        if query.state == QueryState.COMPLETE:
             query.state = QueryState.INIT
             query.item_name = None
             query.currency_name = None

        # Parse the message for item and currency
        extracted_item, extracted_currency = self._parse_price_request(message)

        if extracted_item:
            query.item_name = extracted_item
        if extracted_currency:
            query.currency_name = extracted_currency
        
        # Update state based on what we have
        if not query.item_name:
            query.state = QueryState.AWAITING_ITEM
            return "What item are you interested in?"
        
        if not query.currency_name:
            query.state = QueryState.AWAITING_CURRENCY
            return f"What currency do you want to know the price of a {query.item_name} in?"

        query.state = QueryState.COMPLETE

        if db:
            # New logic: if we have item and currency, look up the price.
            response = asyncio.run(self._lookup_price_async(query.item_name, query.currency_name))
            return response
        else:
            # Fallback if no database is connected
            return f"I will look up the price of a {query.item_name} in {query.currency_name}."

    async def _lookup_price_async(self, item: str, currency: str, league: str = "Standard") -> str:
        """Looks up the price of an item in a given currency."""
        # Normalize currency name (e.g., "chaos orbs" -> "chaos")
        normalized_currency = currency.lower().replace(" orbs", "").replace(" orb", "")

        # Normalize item name
        item_name_map = {"divine": "Divine Orb", "divine orb": "Divine Orb", "chaos": "Chaos Orb", "chaos orb": "Chaos Orb"}
        normalized_item = item_name_map.get(item.lower(), item.title())


        item_row = await pricing.get_item_price(normalized_item, league)

        if not item_row:
            return f"I don’t have current data for {normalized_item}."

        price = await pricing.convert_price(item_row, normalized_currency)

        if price is None:
            return f"I can't express the price of a {normalized_item} in {currency}."

        pretty_item = normalized_item.title()
        pretty_currency = currency.title()

        price_str = f"{price:g}"

        return f"A {pretty_item} is currently {price_str} {pretty_currency}."

    def _parse_price_request(self, message: str) -> (Optional[str], Optional[str]):
        """
        Parses a message to extract item and currency names.
        This is a simple regex-based implementation.
        """
        item = None
        currency = None

        # Pattern: "how many [currency] is a [item] worth"
        match = re.search(r"how many (.*) is a (.*) worth\??", message, re.IGNORECASE)
        if match:
            currency = match.group(1).strip()
            item = match.group(2).strip()
            return item, currency

        # Pattern: "how much is a [item] in [currency]"
        match = re.search(r"how much is a (.*) in (.*)\??", message, re.IGNORECASE)
        if match:
            item = match.group(1).strip()
            currency = match.group(2).strip()
            return item, currency

        # Fallback: Look for item names (simple implementation)
        # In a real app, you'd have a list of items.
        if "divine orb" in message.lower():
            item = "Divine Orb"
        
        # Look for currency names
        if "chaos orb" in message.lower():
            currency = "Chaos Orb"

        return item, currency