{"rustc": 10895048813736897673, "features": "[\"common-controls-v6\", \"compression\", \"custom-protocol\", \"default\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 2241668132362809309, "path": 6282024138805024657, "deps": [[40386456601120721, "percent_encoding", false, 9561711409234557824], [442785307232013896, "tauri_runtime", false, 10032753341566814057], [1200537532907108615, "url<PERSON><PERSON>n", false, 8874508334913728458], [3150220818285335163, "url", false, 4211341816596351554], [4143744114649553716, "raw_window_handle", false, 8005042981042231374], [4341921533227644514, "muda", false, 3894511393970407550], [4919829919303820331, "serialize_to_javascript", false, 9820391795173580492], [5986029879202738730, "log", false, 8821711327181341800], [7752760652095876438, "tauri_runtime_wry", false, 677265885813320550], [8539587424388551196, "webview2_com", false, 13804335643666345152], [9010263965687315507, "http", false, 4892805295673845999], [9228235415475680086, "tauri_macros", false, 7155152701158455564], [9538054652646069845, "tokio", false, 17251417202847693008], [9689903380558560274, "serde", false, 1473350663965462290], [9920160576179037441, "getrandom", false, 8878694089655433105], [10229185211513642314, "mime", false, 3969257402737228237], [10629569228670356391, "futures_util", false, 7918929263035461435], [10755362358622467486, "build_script_build", false, 245632367332990512], [10806645703491011684, "thiserror", false, 2466156708347583921], [11050281405049894993, "tauri_utils", false, 8511169775403936498], [11989259058781683633, "dunce", false, 10028393902584335263], [12565293087094287914, "window_vibrancy", false, 6426598243647076449], [12986574360607194341, "serde_repr", false, 5572589746689857618], [13077543566650298139, "heck", false, 17371707850263138040], [13625485746686963219, "anyhow", false, 4877693705937340862], [14585479307175734061, "windows", false, 4653572282899060777], [15367738274754116744, "serde_json", false, 1469785836717808546], [16928111194414003569, "dirs", false, 12383796321008082566], [17155886227862585100, "glob", false, 3866170920893340160]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-6e66e825d5709327\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}