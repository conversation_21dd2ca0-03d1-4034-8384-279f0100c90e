version: '3.8'

services:
  # PostgreSQL database
  postgres:
    image: postgres:15-alpine
    container_name: poe_dashboard_db
    environment:
      POSTGRES_DB: poe_dashboard
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d poe_dashboard"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - poe_network

  # Redis for caching and background tasks
  redis:
    image: redis:7-alpine
    container_name: poe_dashboard_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - poe_network

  # PoE Dashboard Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: poe_dashboard_app
    environment:
      # Database configuration
      DATABASE_URL: ********************************************/poe_dashboard
      
      # API configuration
      POE_NINJA_BASE_URL: https://poe.ninja/api/data
      POE_DEFAULT_LEAGUE: Mercenaries
      API_MAX_CONCURRENT: 6
      API_TIMEOUT: 30
      API_MAX_REQ_PER_MIN: 45
      API_MAX_ENDPOINT_REQ_PER_MIN: 5
      
      # Scheduler configuration
      SCHEDULER_POLL_INTERVAL: 31
      SCHEDULER_OFFSET: 1
      SCHEDULER_JITTER: 5
      SCHEDULER_MAX_FAILURES: 3
      SCHEDULER_BACKOFF: 60
      SCHEDULER_AUTO_RESTART: true
      
      # Validation configuration
      VALIDATION_MIN_LISTING_COUNT: 10
      VALIDATION_MIN_CONFIDENCE_COUNT: 5
      VALIDATION_MAX_OUTLIER_SIGMA: 4.0
      VALIDATION_CHAOS_MIN: 0.5
      VALIDATION_CHAOS_MAX: 100000.0
      
      # Web server configuration
      WEB_HOST: 0.0.0.0
      WEB_PORT: 8000
      DEBUG: false
      CORS_ORIGINS: http://localhost:3000,http://localhost:8080
      
      # Logging configuration
      LOG_LEVEL: INFO
      LOG_FILE_PATH: /app/logs/poe_dashboard.log
      
      # Environment
      ENVIRONMENT: production
      
      # Redis configuration
      REDIS_URL: redis://redis:6379/0
      
    ports:
      - "8000:8000"
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    networks:
      - poe_network

  # Monitoring dashboard (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: poe_dashboard_grafana
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
      GF_USERS_ALLOW_SIGN_UP: false
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - poe_network

  # Prometheus for metrics collection (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: poe_dashboard_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - poe_network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  grafana_data:
    driver: local
  prometheus_data:
    driver: local

networks:
  poe_network:
    driver: bridge
