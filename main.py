import asyncio
from fastapi import FastAP<PERSON>
from pydantic import BaseModel

from chat.manager import <PERSON><PERSON><PERSON><PERSON><PERSON>
from database import init as db_init, close as db_close

app = FastAPI()
chat_manager = ChatManager()

@app.on_event("startup")
async def startup_event():
    """Initialise database connection on startup."""
    await db_init()

@app.on_event("shutdown")
async def shutdown_event():
    """Close database connection on shutdown."""
    await db_close()

class ChatRequest(BaseModel):
    session_id: str
    message: str

class ChatResponse(BaseModel):
    response: str

@app.post("/chat", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest):
    """Endpoint to handle chat messages."""
    response_message = chat_manager.process_message(request.session_id, request.message)
    return ChatResponse(response=response_message)

@app.get("/")
async def root():
    return {"message": "Welcome to the PoE Dashboard Chat API"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
