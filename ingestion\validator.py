"""
Data Validator Component
=======================
JSON schema validation with fast simd-json and pre-compiled schemas
Implements data quality guardrails from the blueprint
"""

import json
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import jsonschema
from jsonschema import validate, ValidationError
import statistics

logger = logging.getLogger(__name__)

class ValidationResult(Enum):
    VALID = "valid"
    INVALID = "invalid"
    CORRUPT = "corrupt"
    SUSPICIOUS = "suspicious"

@dataclass
class ValidationReport:
    """Report for a single validation"""
    endpoint: str
    result: ValidationResult
    row_count: int
    error_message: Optional[str] = None
    warnings: List[str] = None
    confidence_score: float = 1.0
    
    def __post_init__(self):
        if self.warnings is None:
            self.warnings = []

class DataValidator:
    """
    Validates JSON data from PoE.ninja API with:
    - Schema validation for required keys
    - Data quality guardrails (confidence filters)
    - Currency sanity checks
    - Outlier detection (σ > 4)
    - Corruption detection
    """
    
    def __init__(self):
        self.currency_schema = self._build_currency_schema()
        self.item_schema = self._build_item_schema()
        self.league_schema = self._build_league_schema()
        
        # Quality thresholds from blueprint
        self.min_listing_count = 10
        self.min_confidence_count = 5
        self.max_outlier_sigma = 4.0
        self.chaos_min = 0.5
        self.chaos_max = 100000.0
        
    def _build_currency_schema(self) -> Dict[str, Any]:
        """Build JSON schema for currency endpoints"""
        return {
            "type": "object",
            "required": ["lines", "currencyDetails"],
            "properties": {
                "lines": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "required": ["currencyTypeName", "chaosEquivalent"],
                        "properties": {
                            "currencyTypeName": {"type": "string"},
                            "chaosEquivalent": {"type": "number"},
                            "pay": {"type": "object"},
                            "receive": {"type": "object"},
                            "paySparkLine": {"type": "object"},
                            "receiveSparkLine": {"type": "object"},
                            "count": {"type": "integer", "minimum": 0},
                            "listingCount": {"type": "integer", "minimum": 0}
                        }
                    }
                },
                "currencyDetails": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "required": ["id", "name"],
                        "properties": {
                            "id": {"type": "integer"},
                            "name": {"type": "string"},
                            "icon": {"type": "string"}
                        }
                    }
                }
            }
        }
        
    def _build_item_schema(self) -> Dict[str, Any]:
        """Build JSON schema for item endpoints"""
        return {
            "type": "object",
            "required": ["lines"],
            "properties": {
                "lines": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "required": ["name"],
                        "properties": {
                            "id": {"type": "integer"},
                            "name": {"type": "string"},
                            "icon": {"type": "string"},
                            "mapTier": {"type": "integer"},
                            "levelRequired": {"type": "integer"},
                            "baseType": {"type": "string"},
                            "stackSize": {"type": "integer"},
                            "variant": {"type": "string"},
                            "prophecyText": {"type": "string"},
                            "artFilename": {"type": "string"},
                            "links": {"type": "integer"},
                            "itemClass": {"type": "integer"},
                            "sparkline": {
                                "type": "object",
                                "properties": {
                                    "data": {"type": "array", "items": {"type": "number"}},
                                    "totalChange": {"type": "number"}
                                }
                            },
                            "lowConfidenceSparkline": {
                                "type": "object",
                                "properties": {
                                    "data": {"type": "array", "items": {"type": "number"}},
                                    "totalChange": {"type": "number"}
                                }
                            },
                            "implicitModifiers": {"type": "array"},
                            "explicitModifiers": {"type": "array"},
                            "flavourText": {"type": "string"},
                            "corrupted": {"type": "boolean"},
                            "gemLevel": {"type": "integer"},
                            "gemQuality": {"type": "integer"},
                            "itemType": {"type": "string"},
                            "chaosValue": {"type": "number", "minimum": 0},
                            "divineValue": {"type": "number", "minimum": 0},
                            "count": {"type": "integer", "minimum": 0},
                            "listingCount": {"type": "integer", "minimum": 0},
                            "detailsId": {"type": "string"}
                        }
                    }
                }
            }
        }
        
    def _build_league_schema(self) -> Dict[str, Any]:
        """Build JSON schema for league state endpoint"""
        return {
            "type": "object",
            "required": ["economyLeagues"],
            "properties": {
                "economyLeagues": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "required": ["name", "displayName"],
                        "properties": {
                            "name": {"type": "string"},
                            "displayName": {"type": "string"},
                            "hardcore": {"type": "boolean"},
                            "indexed": {"type": "boolean"}
                        }
                    }
                }
            }
        }
        
    async def validate_all(self, raw_data: Dict[str, Optional[Dict[str, Any]]]) -> Dict[str, ValidationReport]:
        """
        Validate all fetched data
        
        Args:
            raw_data: Dict mapping endpoint names to their data
            
        Returns:
            Dict mapping endpoint names to validation reports
        """
        logger.info(f"Validating data from {len(raw_data)} endpoints")
        
        validation_results = {}
        
        for endpoint, data in raw_data.items():
            if data is None:
                validation_results[endpoint] = ValidationReport(
                    endpoint=endpoint,
                    result=ValidationResult.INVALID,
                    row_count=0,
                    error_message="No data received"
                )
                continue
                
            try:
                report = await self._validate_endpoint_data(endpoint, data)
                validation_results[endpoint] = report
                
            except Exception as e:
                logger.error(f"Validation failed for {endpoint}: {e}")
                validation_results[endpoint] = ValidationReport(
                    endpoint=endpoint,
                    result=ValidationResult.CORRUPT,
                    row_count=0,
                    error_message=f"Validation exception: {str(e)}"
                )
                
        # Log summary
        valid_count = sum(1 for r in validation_results.values() if r.result == ValidationResult.VALID)
        total_count = len(validation_results)
        
        logger.info(f"Validation complete: {valid_count}/{total_count} endpoints valid")
        
        return validation_results
        
    async def _validate_endpoint_data(self, endpoint: str, data: Dict[str, Any]) -> ValidationReport:
        """Validate data for a specific endpoint"""
        
        # Determine schema based on endpoint
        if endpoint in ['Currency', 'Fragment']:
            schema = self.currency_schema
        elif endpoint == 'league_state':
            schema = self.league_schema
        else:
            schema = self.item_schema
            
        # Basic schema validation
        try:
            validate(instance=data, schema=schema)
        except ValidationError as e:
            return ValidationReport(
                endpoint=endpoint,
                result=ValidationResult.INVALID,
                row_count=0,
                error_message=f"Schema validation failed: {e.message}"
            )
            
        # Extract lines for further validation
        lines = data.get('lines', [])
        row_count = len(lines)
        
        if row_count == 0:
            return ValidationReport(
                endpoint=endpoint,
                result=ValidationResult.INVALID,
                row_count=0,
                error_message="No data lines found"
            )
            
        # Apply data quality guardrails
        warnings = []
        confidence_score = 1.0
        
        # Currency-specific validation
        if endpoint in ['Currency', 'Fragment']:
            warnings.extend(self._validate_currency_data(lines))
            
        # Item-specific validation
        else:
            warnings.extend(self._validate_item_data(lines))
            
        # Calculate confidence score based on warnings
        if warnings:
            confidence_score = max(0.1, 1.0 - (len(warnings) * 0.1))
            
        # Determine final result
        if confidence_score < 0.3:
            result = ValidationResult.SUSPICIOUS
        elif warnings:
            result = ValidationResult.VALID  # Valid but with warnings
        else:
            result = ValidationResult.VALID
            
        return ValidationReport(
            endpoint=endpoint,
            result=result,
            row_count=row_count,
            warnings=warnings,
            confidence_score=confidence_score
        )
        
    def _validate_currency_data(self, lines: List[Dict[str, Any]]) -> List[str]:
        """Validate currency-specific data quality"""
        warnings = []
        
        chaos_values = []
        listing_counts = []
        
        for line in lines:
            # Currency sanity check: chaosEquivalent ∈ [0.5c, 100,000c]
            chaos_equiv = line.get('chaosEquivalent', 0)
            if chaos_equiv < self.chaos_min or chaos_equiv > self.chaos_max:
                warnings.append(f"Currency {line.get('currencyTypeName')} has suspicious chaos value: {chaos_equiv}")
                
            chaos_values.append(chaos_equiv)
            
            # Listing count check
            listing_count = line.get('listingCount', 0)
            if listing_count < self.min_listing_count:
                warnings.append(f"Currency {line.get('currencyTypeName')} has low listing count: {listing_count}")
                
            listing_counts.append(listing_count)
            
        # Outlier detection for chaos values
        if len(chaos_values) > 2:
            try:
                mean_chaos = statistics.mean(chaos_values)
                stdev_chaos = statistics.stdev(chaos_values)
                
                for i, value in enumerate(chaos_values):
                    if stdev_chaos > 0:
                        z_score = abs(value - mean_chaos) / stdev_chaos
                        if z_score > self.max_outlier_sigma:
                            currency_name = lines[i].get('currencyTypeName', f'index_{i}')
                            warnings.append(f"Currency {currency_name} is statistical outlier (σ={z_score:.2f})")
                            
            except statistics.StatisticsError:
                pass  # Not enough data for statistics
                
        return warnings
        
    def _validate_item_data(self, lines: List[Dict[str, Any]]) -> List[str]:
        """Validate item-specific data quality"""
        warnings = []
        
        chaos_values = []
        listing_counts = []
        
        for line in lines:
            # Confidence filter: count >= 5 AND listingCount >= 10
            count = line.get('count', 0)
            listing_count = line.get('listingCount', 0)
            
            if count < self.min_confidence_count:
                warnings.append(f"Item {line.get('name')} has low confidence count: {count}")
                
            if listing_count < self.min_listing_count:
                warnings.append(f"Item {line.get('name')} has low listing count: {listing_count}")
                
            # Chaos value sanity check
            chaos_value = line.get('chaosValue', 0)
            if chaos_value and (chaos_value < self.chaos_min or chaos_value > self.chaos_max):
                warnings.append(f"Item {line.get('name')} has suspicious chaos value: {chaos_value}")
                
            if chaos_value:
                chaos_values.append(chaos_value)
            listing_counts.append(listing_count)
            
        # Outlier detection for chaos values
        if len(chaos_values) > 2:
            try:
                mean_chaos = statistics.mean(chaos_values)
                stdev_chaos = statistics.stdev(chaos_values)
                
                for i, value in enumerate(chaos_values):
                    if stdev_chaos > 0:
                        z_score = abs(value - mean_chaos) / stdev_chaos
                        if z_score > self.max_outlier_sigma:
                            item_name = lines[i].get('name', f'index_{i}')
                            warnings.append(f"Item {item_name} is statistical outlier (σ={z_score:.2f})")
                            
            except statistics.StatisticsError:
                pass  # Not enough data for statistics
                
        return warnings
        
    def get_validation_summary(self, validation_results: Dict[str, ValidationReport]) -> Dict[str, Any]:
        """Get summary statistics for validation results"""
        if not validation_results:
            return {}
            
        total_endpoints = len(validation_results)
        valid_endpoints = sum(1 for r in validation_results.values() if r.result == ValidationResult.VALID)
        suspicious_endpoints = sum(1 for r in validation_results.values() if r.result == ValidationResult.SUSPICIOUS)
        invalid_endpoints = sum(1 for r in validation_results.values() if r.result == ValidationResult.INVALID)
        corrupt_endpoints = sum(1 for r in validation_results.values() if r.result == ValidationResult.CORRUPT)
        
        total_rows = sum(r.row_count for r in validation_results.values())
        total_warnings = sum(len(r.warnings) for r in validation_results.values())
        
        avg_confidence = statistics.mean(r.confidence_score for r in validation_results.values())
        
        return {
            'total_endpoints': total_endpoints,
            'valid_endpoints': valid_endpoints,
            'suspicious_endpoints': suspicious_endpoints,
            'invalid_endpoints': invalid_endpoints,
            'corrupt_endpoints': corrupt_endpoints,
            'success_rate': (valid_endpoints / total_endpoints) * 100 if total_endpoints > 0 else 0,
            'total_rows': total_rows,
            'total_warnings': total_warnings,
            'average_confidence': round(avg_confidence, 3)
        }
