use tauri::State;
use chrono::Utc;

use crate::{AppState, models::*};

#[tauri::command]
pub async fn get_market_summary(
    league: String,
    state: State<'_, AppState>,
) -> Result<MarketSummary, String> {
    // Get total items count
    let total_items = sqlx::query_scalar::<_, i64>(
        "SELECT COUNT(*) FROM items i JOIN leagues l ON i.league_id = l.id WHERE l.name = ?"
    )
    .bind(&league)
    .fetch_one(state.db_manager.get_pool())
    .await
    .unwrap_or(0);
    
    // Get active leagues count
    let active_leagues = sqlx::query_scalar::<_, i64>(
        "SELECT COUNT(*) FROM leagues WHERE is_active = 1"
    )
    .fetch_one(state.db_manager.get_pool())
    .await
    .unwrap_or(0);
    
    // Get top movers (simplified)
    let top_movers = vec![
        TopMover {
            item_name: "Divine Orb".to_string(),
            change_pct: 5.2,
            current_price: 200.0,
            volume: 150,
        },
        TopMover {
            item_name: "Chaos Orb".to_string(),
            change_pct: -2.1,
            current_price: 1.0,
            volume: 500,
        },
    ];
    
    // Currency rates (simplified)
    let currency_rates = CurrencyRates {
        chaos_to_divine: 0.005,
        divine_to_chaos: 200.0,
        exalt_to_chaos: Some(150.0),
    };
    
    Ok(MarketSummary {
        total_items,
        active_leagues,
        last_update: Utc::now(),
        top_movers,
        currency_rates,
    })
}

#[tauri::command]
pub async fn get_league_list(
    state: State<'_, AppState>,
) -> Result<Vec<League>, String> {
    let leagues = state.db_manager
        .get_leagues()
        .await
        .map_err(|e| format!("Failed to get leagues: {}", e))?;
    
    Ok(leagues)
}

#[tauri::command]
pub async fn set_current_league(
    league: String,
    state: State<'_, AppState>,
) -> Result<(), String> {
    // This would typically update user preferences
    // For now, just log the change
    log::info!("Current league set to: {}", league);
    Ok(())
}
