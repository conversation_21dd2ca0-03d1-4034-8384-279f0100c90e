{"rustc": 10895048813736897673, "features": "[\"common-controls-v6\", \"compression\", \"custom-protocol\", \"default\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 6282024138805024657, "deps": [[40386456601120721, "percent_encoding", false, 10260631236887431270], [442785307232013896, "tauri_runtime", false, 13651042032654462003], [1200537532907108615, "url<PERSON><PERSON>n", false, 6022586645049297509], [3150220818285335163, "url", false, 2358920778526070859], [4143744114649553716, "raw_window_handle", false, 11505498693017358128], [4341921533227644514, "muda", false, 67170215982472617], [4919829919303820331, "serialize_to_javascript", false, 17673699996659977310], [5986029879202738730, "log", false, 9041289895326090971], [7752760652095876438, "tauri_runtime_wry", false, 16157114391066017888], [8539587424388551196, "webview2_com", false, 12310748178939823266], [9010263965687315507, "http", false, 2407541516320360975], [9228235415475680086, "tauri_macros", false, 2500244269958428038], [9538054652646069845, "tokio", false, 8706575809721943979], [9689903380558560274, "serde", false, 7414200475410930407], [9920160576179037441, "getrandom", false, 12086343275885591349], [10229185211513642314, "mime", false, 4599952621902664255], [10629569228670356391, "futures_util", false, 14060607619919236999], [10755362358622467486, "build_script_build", false, 1804714126154784306], [10806645703491011684, "thiserror", false, 2421778471660001397], [11050281405049894993, "tauri_utils", false, 7489821061439082610], [11989259058781683633, "dunce", false, 12015258817262054498], [12565293087094287914, "window_vibrancy", false, 11587481569331978046], [12986574360607194341, "serde_repr", false, 5572589746689857618], [13077543566650298139, "heck", false, 17322163720383359805], [13625485746686963219, "anyhow", false, 17387659364330292750], [14585479307175734061, "windows", false, 13752367330713928517], [15367738274754116744, "serde_json", false, 15399624556204434903], [16928111194414003569, "dirs", false, 17847412115801632661], [17155886227862585100, "glob", false, 3681942166429823739]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-b7cc428aac569ed6\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}