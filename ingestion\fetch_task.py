"""
Fetch Task Component
===================
Implements parallel fetch with retry logic and metrics collection
Based on blueprint requirements for robust data acquisition
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
import json
from dataclasses import dataclass

from poe_ninja_api import PoeNinjaAPI, EndpointType, FetchMetrics
from database.connection import DatabaseConnection

logger = logging.getLogger(__name__)

@dataclass
class FetchResult:
    """Result of a fetch operation"""
    endpoint: str
    success: bool
    data: Optional[Dict[str, Any]]
    metrics: FetchMetrics
    error: Optional[str] = None

class FetchTask:
    """
    Handles parallel fetching of all PoE.ninja endpoints with:
    - Maximum 6 concurrent requests (blueprint requirement)
    - Jittered start times (±5s) to avoid thundering herd
    - 3x retry with exponential backoff (0.5s, 1s, 2s)
    - Comprehensive metrics collection for health dashboard
    """
    
    def __init__(self, api_client: PoeNinjaAPI, database_url: str):
        self.api_client = api_client
        self.database_url = database_url
        self.db_connection: Optional[DatabaseConnection] = None
        
        # Blueprint requirements
        self.max_concurrent = 6
        self.jitter_range = 5  # ±5 seconds
        self.max_retries = 3
        self.retry_delays = [0.5, 1.0, 2.0]  # Exponential backoff
        
    async def initialize(self):
        """Initialize database connection"""
        self.db_connection = DatabaseConnection(self.database_url)
        await self.db_connection.connect()
        
    async def fetch_all_data(self) -> Dict[str, Optional[Dict[str, Any]]]:
        """
        Fetch data from all endpoints in parallel
        
        Returns:
            Dict mapping endpoint names to their data (or None if failed)
        """
        logger.info("Starting parallel fetch of all endpoints")
        logger.debug(f"Fetch configuration - League: {self.api_client.league}, "
                   f"Max concurrent: {self.max_concurrent}, "
                   f"Jitter range: ±{self.jitter_range}s, Max retries: {self.max_retries}")
        start_time = datetime.now()

        # Get current league state first
        logger.debug("Fetching league state...")
        league_data = await self._fetch_league_state()

        # Define all endpoints to fetch
        currency_endpoints = [EndpointType.CURRENCY, EndpointType.FRAGMENT]
        item_endpoints = [e for e in EndpointType if e not in currency_endpoints]
        all_endpoints = currency_endpoints + item_endpoints

        logger.debug(f"Currency endpoints: {[e.value for e in currency_endpoints]}")
        logger.debug(f"Item endpoints: {[e.value for e in item_endpoints]}")
        logger.debug(f"Total endpoints to fetch: {len(all_endpoints)}")

        # Create semaphore for concurrency control (max 6 concurrent)
        semaphore = asyncio.Semaphore(self.max_concurrent)
        logger.debug(f"Created semaphore with {self.max_concurrent} permits")

        # Create fetch tasks with jittered start times
        tasks = []
        for i, endpoint in enumerate(all_endpoints):
            # Add jitter to spread requests over ±5 seconds
            jitter_delay = (i % (self.jitter_range * 2)) - self.jitter_range
            if jitter_delay < 0:
                jitter_delay = 0

            logger.debug(f"Scheduling {endpoint.value} with {jitter_delay}s jitter")
            task = asyncio.create_task(
                self._fetch_endpoint_with_jitter(semaphore, endpoint, jitter_delay)
            )
            tasks.append(task)
            
        # Execute all fetches
        logger.debug("Starting parallel execution of all fetch tasks...")
        fetch_start_time = datetime.now()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        fetch_duration = (datetime.now() - fetch_start_time).total_seconds()

        # Process results
        data_dict = {}
        successful_fetches = 0
        failed_fetches = 0
        total_rows = 0

        for result in results:
            if isinstance(result, Exception):
                logger.error(f"Fetch task failed with exception: {result}")
                failed_fetches += 1
                continue

            fetch_result: FetchResult = result
            data_dict[fetch_result.endpoint] = fetch_result.data

            if fetch_result.success:
                successful_fetches += 1
                if fetch_result.data and 'lines' in fetch_result.data:
                    rows = len(fetch_result.data['lines'])
                    total_rows += rows
                    logger.debug(f"Successfully fetched {fetch_result.endpoint}: {rows} rows")
                else:
                    logger.debug(f"Successfully fetched {fetch_result.endpoint}: no data")
            else:
                failed_fetches += 1
                logger.debug(f"Failed to fetch {fetch_result.endpoint}: {fetch_result.error}")

            # Store metrics in database
            if fetch_result.metrics:
                await self._store_fetch_metrics(fetch_result.metrics)

        duration = (datetime.now() - start_time).total_seconds()

        logger.info(
            f"Parallel fetch completed in {fetch_duration:.2f}s: "
            f"{successful_fetches}/{len(all_endpoints)} successful, "
            f"{failed_fetches} failed, {total_rows} total rows, total time: {duration:.1f}s"
        )
        logger.debug(f"Successful endpoints: {[k for k, v in data_dict.items() if v is not None]}")
        
        # Add league data to results
        if league_data:
            data_dict['league_state'] = league_data
            
        return data_dict
        
    async def _fetch_league_state(self) -> Optional[Dict[str, Any]]:
        """Fetch current league state for league discovery"""
        try:
            logger.info("Fetching league state")
            data = await self.api_client.get_league_state()
            
            if data:
                logger.info("League state fetched successfully")
                # Store in staging for processing
                await self._store_raw_data('league_state', 'getindexstate', data)
                
            return data
            
        except Exception as e:
            logger.error(f"Failed to fetch league state: {e}")
            return None
            
    async def _fetch_endpoint_with_jitter(self,
                                        semaphore: asyncio.Semaphore,
                                        endpoint: EndpointType,
                                        jitter_delay: float) -> FetchResult:
        """Fetch a single endpoint with jitter and retry logic"""

        # Apply jitter delay
        if jitter_delay > 0:
            logger.debug(f"Applying {jitter_delay}s jitter delay for {endpoint.value}")
            await asyncio.sleep(jitter_delay)

        logger.debug(f"Acquiring semaphore for {endpoint.value}")
        async with semaphore:
            logger.debug(f"Starting fetch for {endpoint.value}")
            return await self._fetch_endpoint_with_retry(endpoint)
            
    async def _fetch_endpoint_with_retry(self, endpoint: EndpointType) -> FetchResult:
        """Fetch endpoint with retry logic"""

        logger.debug(f"Starting fetch with retry for {endpoint.value} (max {self.max_retries} retries)")

        for attempt in range(self.max_retries + 1):
            try:
                logger.debug(f"Attempt {attempt + 1}/{self.max_retries + 1} for {endpoint.value}")

                # Determine fetch method based on endpoint type
                if endpoint == EndpointType.CURRENCY:
                    data = await self.api_client.get_currency_data()
                elif endpoint == EndpointType.FRAGMENT:
                    data = await self.api_client.get_fragments_data()
                else:
                    data = await self.api_client.get_items_data(endpoint)

                if data is not None:
                    rows = len(data.get('lines', []))
                    logger.debug(f"Successfully fetched {endpoint.value}: {rows} rows")

                    # Store raw data in staging
                    await self._store_raw_data(
                        self.api_client.league,
                        endpoint.value,
                        data
                    )

                    # Get metrics from API client
                    metrics = self._get_latest_metrics(endpoint.value)

                    return FetchResult(
                        endpoint=endpoint.value,
                        success=True,
                        data=data,
                        metrics=metrics
                    )
                else:
                    logger.debug(f"No data returned for {endpoint.value} on attempt {attempt + 1}")
                    if attempt == self.max_retries:
                        metrics = self._get_latest_metrics(endpoint.value)
                        return FetchResult(
                            endpoint=endpoint.value,
                            success=False,
                            data=None,
                            metrics=metrics,
                            error="No data returned from API"
                        )
                    else:
                        # Wait before retry
                        retry_delay = self.retry_delays[attempt]
                        logger.debug(f"Retrying {endpoint.value} in {retry_delay}s...")
                        await asyncio.sleep(retry_delay)

            except Exception as e:
                error_msg = f"Attempt {attempt + 1} failed for {endpoint.value}: {str(e)}"
                logger.warning(error_msg)
                logger.debug(f"Exception details for {endpoint.value}: {type(e).__name__}: {str(e)}")

                if attempt == self.max_retries:
                    metrics = self._get_latest_metrics(endpoint.value)
                    return FetchResult(
                        endpoint=endpoint.value,
                        success=False,
                        data=None,
                        metrics=metrics,
                        error=str(e)
                    )
                else:
                    # Wait before retry with exponential backoff
                    retry_delay = self.retry_delays[attempt]
                    logger.debug(f"Retrying {endpoint.value} in {retry_delay}s after exception...")
                    await asyncio.sleep(retry_delay)
                    
        # Should never reach here, but just in case
        return FetchResult(
            endpoint=endpoint.value,
            success=False,
            data=None,
            metrics=None,
            error="Maximum retries exceeded"
        )
        
    def _get_latest_metrics(self, endpoint: str) -> Optional[FetchMetrics]:
        """Get the latest metrics for an endpoint from the API client"""
        if not self.api_client.metrics:
            return None
            
        # Find the most recent metrics for this endpoint
        for metrics in reversed(self.api_client.metrics):
            if metrics.endpoint == endpoint:
                return metrics
                
        return None
        
    async def _store_raw_data(self, league: str, endpoint_type: str, data: Dict[str, Any]):
        """Store raw JSON data in staging table"""
        if not self.db_connection:
            logger.warning("No database connection, skipping raw data storage")
            return
            
        try:
            query = """
                INSERT INTO staging_raw (league_name, endpoint_type, raw_data, fetched_at)
                VALUES ($1, $2, $3, $4)
            """
            
            await self.db_connection.execute(
                query,
                league,
                endpoint_type,
                json.dumps(data),
                datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Failed to store raw data for {endpoint_type}: {e}")
            
    async def _store_fetch_metrics(self, metrics: Optional[FetchMetrics]):
        """Store fetch metrics in stats_fetch table"""
        if not metrics or not self.db_connection:
            return
            
        try:
            query = """
                INSERT INTO stats_fetch (
                    endpoint, league_name, status, latency_ms, etag, 
                    rows_ingested, error_message, fetch_time
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            """
            
            await self.db_connection.execute(
                query,
                metrics.endpoint,
                metrics.league,
                metrics.status_code,
                metrics.latency_ms,
                metrics.etag,
                metrics.rows_ingested,
                metrics.error,
                metrics.timestamp
            )
            
        except Exception as e:
            logger.error(f"Failed to store fetch metrics: {e}")
            
    async def get_fetch_health_metrics(self) -> Dict[str, Any]:
        """Get health metrics for the fetch operations"""
        if not self.db_connection:
            return {}
            
        try:
            # Get metrics from last hour
            query = """
                SELECT 
                    endpoint,
                    COUNT(*) as total_requests,
                    COUNT(*) FILTER (WHERE status = 200) as successful_requests,
                    AVG(latency_ms) as avg_latency_ms,
                    MAX(latency_ms) as max_latency_ms,
                    SUM(rows_ingested) as total_rows
                FROM stats_fetch 
                WHERE fetch_time >= NOW() - INTERVAL '1 hour'
                GROUP BY endpoint
                ORDER BY endpoint
            """
            
            results = await self.db_connection.fetch_all(query)
            
            health_metrics = {}
            for row in results:
                endpoint = row['endpoint']
                success_rate = (row['successful_requests'] / row['total_requests']) * 100
                
                health_metrics[endpoint] = {
                    'total_requests': row['total_requests'],
                    'success_rate_percent': round(success_rate, 2),
                    'avg_latency_ms': round(row['avg_latency_ms'], 2) if row['avg_latency_ms'] else 0,
                    'max_latency_ms': row['max_latency_ms'] or 0,
                    'total_rows': row['total_rows'] or 0
                }
                
            return health_metrics
            
        except Exception as e:
            logger.error(f"Failed to get fetch health metrics: {e}")
            return {}
            
    async def cleanup(self):
        """Cleanup resources"""
        if self.db_connection:
            await self.db_connection.close()
