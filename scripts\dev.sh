#!/bin/bash

# Development script for PoE Profit AI

echo "🚀 Starting PoE Profit AI Development Environment"

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18 or later."
    exit 1
fi

# Check if Rust is installed
if ! command -v cargo &> /dev/null; then
    echo "❌ Rust is not installed. Please install Rust from https://rustup.rs/"
    exit 1
fi

# Install frontend dependencies
echo "📦 Installing frontend dependencies..."
npm install

# Install Tauri CLI if not present
if ! command -v cargo-tauri &> /dev/null; then
    echo "🔧 Installing Tauri CLI..."
    cargo install tauri-cli
fi

# Start development server
echo "🏃 Starting development server..."
npm run tauri:dev
