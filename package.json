{"name": "poe-profit-ai", "private": true, "version": "1.0.0", "type": "module", "description": "PoE Profit AI - Stand-Alone Desktop Edition", "author": "PoE Profit AI Team", "license": "MIT", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build", "test": "vitest", "test:ui": "vitest --ui", "test:e2e": "playwright test", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write .", "type-check": "tsc --noEmit"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.1", "@tauri-apps/api": "^2.5.0", "@tauri-apps/plugin-shell": "^2.2.1", "@tauri-apps/plugin-store": "^2.0.0", "@tauri-apps/plugin-updater": "^2.0.0", "@tauri-apps/plugin-window-state": "^2.0.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.5", "lodash-es": "^4.17.21", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "recharts": "^2.8.0", "sonner": "^2.0.5", "tailwind-merge": "^2.0.0", "zustand": "^4.4.7"}, "devDependencies": {"@playwright/test": "^1.40.0", "@tauri-apps/cli": "^2.0.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "@types/lodash-es": "^4.17.12", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "@vitest/ui": "^0.34.6", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "jsdom": "^22.1.0", "postcss": "^8.4.31", "prettier": "^3.0.3", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0", "vitest": "^0.34.6"}}