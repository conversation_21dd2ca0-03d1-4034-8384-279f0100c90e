import React, { useState, useCallback, useRef } from 'react';
import { Search, TrendingUp, TrendingDown, Minus, Copy } from 'lucide-react';
import { useAppStore } from '../../stores/appStore';
import { quickPriceLookup, searchItemNames, formatCurrency, formatPercentage, getLiquidityColor } from '../../utils/tauri';
import type { PriceResult } from '../../types';
import { debounce } from 'lodash-es';

const QuickPrice: React.FC = () => {
  const [query, setQuery] = useState('');
  const [result, setResult] = useState<PriceResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const { currentLeague, setLastQueryTime } = useAppStore();
  const inputRef = useRef<HTMLInputElement>(null);

  // Debounced search for suggestions
  const debouncedSearch = useCallback(
    debounce(async (searchQuery: string) => {
      if (searchQuery.length >= 2) {
        try {
          const items = await searchItemNames(searchQuery, 8);
          setSuggestions(items);
          setShowSuggestions(true);
        } catch (error) {
          console.error('Search failed:', error);
          setSuggestions([]);
        }
      } else {
        setSuggestions([]);
        setShowSuggestions(false);
      }
    }, 300),
    []
  );

  const handleSearch = async (itemName: string) => {
    if (!itemName.trim()) return;
    
    const startTime = performance.now();
    setLoading(true);
    setShowSuggestions(false);
    
    try {
      const priceResult = await quickPriceLookup({
        item_name: itemName,
        league: currentLeague,
      });
      
      setResult(priceResult);
      
      // Add to recent searches
      setRecentSearches(prev => {
        const updated = [itemName, ...prev.filter(item => item !== itemName)];
        return updated.slice(0, 5);
      });
      
      const queryTime = performance.now() - startTime;
      setLastQueryTime(queryTime);
      
    } catch (error) {
      console.error('Price lookup failed:', error);
      setResult(null);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (value: string) => {
    setQuery(value);
    debouncedSearch(value);
  };

  const handleSuggestionClick = (suggestion: string) => {
    setQuery(suggestion);
    setSuggestions([]);
    setShowSuggestions(false);
    handleSearch(suggestion);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch(query);
    } else if (e.key === 'Escape') {
      setShowSuggestions(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const getTrendIcon = (trend: number | null | undefined) => {
    if (!trend) return <Minus className="w-4 h-4 text-gray-400" />;
    if (trend > 0) return <TrendingUp className="w-4 h-4 text-green-500" />;
    return <TrendingDown className="w-4 h-4 text-red-500" />;
  };

  const getTrendColor = (trend: number | null | undefined) => {
    if (!trend) return 'text-gray-400';
    return trend > 0 ? 'text-green-500' : 'text-red-500';
  };

  return (
    <div className="p-6 max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          Quick Price Lookup
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Get instant price information for any Path of Exile item
        </p>
      </div>

      {/* Search Section */}
      <div className="card p-6">
        <div className="relative mb-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              ref={inputRef}
              type="text"
              value={query}
              onChange={(e) => handleInputChange(e.target.value)}
              onKeyPress={handleKeyPress}
              onFocus={() => suggestions.length > 0 && setShowSuggestions(true)}
              onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
              placeholder="Type or paste item name (e.g., 'Tabula Rasa', 'Divine Orb')"
              className="w-full pl-10 pr-4 py-3 text-lg border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus-ring"
              data-testid="price-search-input"
            />
          </div>
          
          {/* Suggestions Dropdown */}
          {showSuggestions && suggestions.length > 0 && (
            <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-64 overflow-y-auto">
              {suggestions.map((suggestion, index) => (
                <button
                  key={index}
                  onClick={() => handleSuggestionClick(suggestion)}
                  className="w-full px-4 py-3 text-left hover:bg-gray-100 dark:hover:bg-gray-600 first:rounded-t-lg last:rounded-b-lg text-gray-900 dark:text-gray-100 border-b border-gray-100 dark:border-gray-600 last:border-b-0"
                >
                  {suggestion}
                </button>
              ))}
            </div>
          )}
        </div>
        
        <button
          onClick={() => handleSearch(query)}
          disabled={loading || !query.trim()}
          className="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed text-lg py-3"
          data-testid="price-search-button"
        >
          {loading ? (
            <span className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
              Searching...
            </span>
          ) : (
            'Get Price'
          )}
        </button>

        {/* Recent Searches */}
        {recentSearches.length > 0 && (
          <div className="mt-4">
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Recent searches:</p>
            <div className="flex flex-wrap gap-2">
              {recentSearches.map((search, index) => (
                <button
                  key={index}
                  onClick={() => {
                    setQuery(search);
                    handleSearch(search);
                  }}
                  className="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                >
                  {search}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Results */}
      {result && (
        <div className="card p-6 animate-slide-up" data-testid="price-result">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {result.item_name}
            </h2>
            <button
              onClick={() => copyToClipboard(result.item_name)}
              className="p-2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
              title="Copy item name"
            >
              <Copy className="w-4 h-4" />
            </button>
          </div>
          
          {/* Price Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div className="text-center p-4 bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 rounded-lg">
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Chaos Value</p>
              <p className="text-4xl font-bold chaos-value" data-testid="chaos-value">
                {formatCurrency(result.chaos_value, 'chaos')}
              </p>
              <button
                onClick={() => result.chaos_value && copyToClipboard(result.chaos_value.toString())}
                className="mt-2 text-xs text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 flex items-center mx-auto"
              >
                <Copy className="w-3 h-3 mr-1" />
                Copy
              </button>
            </div>
            
            <div className="text-center p-4 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg">
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Divine Value</p>
              <p className="text-4xl font-bold divine-value" data-testid="divine-value">
                {formatCurrency(result.divine_value, 'divine')}
              </p>
              <button
                onClick={() => result.divine_value && copyToClipboard(result.divine_value.toString())}
                className="mt-2 text-xs text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 flex items-center mx-auto"
              >
                <Copy className="w-3 h-3 mr-1" />
                Copy
              </button>
            </div>
          </div>
          
          {/* Additional Info */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <div className="text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400">7-Day Trend</p>
              <div className="flex items-center justify-center mt-1">
                {getTrendIcon(result.trend_7d)}
                <span className={`ml-1 font-medium ${getTrendColor(result.trend_7d)}`}>
                  {formatPercentage(result.trend_7d)}
                </span>
              </div>
            </div>
            
            <div className="text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400">Liquidity</p>
              <p className={`font-medium mt-1 ${getLiquidityColor(result.liquidity)}`}>
                {result.liquidity}
              </p>
            </div>
            
            <div className="text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400">Listings</p>
              <p className="font-medium text-gray-900 dark:text-gray-100 mt-1">
                {result.listing_count}
              </p>
            </div>
            
            <div className="text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400">Confidence</p>
              <p className="font-medium text-gray-900 dark:text-gray-100 mt-1">
                {(result.confidence * 100).toFixed(0)}%
              </p>
            </div>
          </div>
          
          {/* Footer */}
          <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-600 text-sm text-gray-500 dark:text-gray-400">
            <span>
              Updated: {new Date(result.last_updated).toLocaleString()}
            </span>
            <span>
              League: {currentLeague}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default QuickPrice;
