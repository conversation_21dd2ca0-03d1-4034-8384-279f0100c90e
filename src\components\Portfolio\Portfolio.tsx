import React, { useState, useEffect } from 'react';
import { Briefcase, Plus, RefreshCw, TrendingUp, TrendingDown } from 'lucide-react';
import { useAppStore } from '../../stores/appStore';
import { getPortfolioItems, getPortfolioTotalValue } from '../../utils/tauri';
import type { PortfolioItem } from '../../types';
import PortfolioItemCard from './PortfolioItemCard';
import AddPortfolioItemModal from './AddPortfolioItemModal';
import LoadingSpinner from '../UI/LoadingSpinner';
import { formatCurrency } from '../../utils/tauri';

const Portfolio: React.FC = () => {
  const [items, setItems] = useState<PortfolioItem[]>([]);
  const [totalValue, setTotalValue] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | undefined>();
  const [showAddModal, setShowAddModal] = useState(false);
  const { currentLeague } = useAppStore();

  useEffect(() => {
    loadPortfolioData();
  }, [currentLeague]);

  const loadPortfolioData = async () => {
    try {
      setLoading(true);
      setError(undefined);
      
      const [portfolioItems, portfolioValue] = await Promise.all([
        getPortfolioItems(currentLeague),
        getPortfolioTotalValue(currentLeague),
      ]);
      
      setItems(portfolioItems);
      setTotalValue(portfolioValue);
    } catch (err) {
      console.error('Failed to load portfolio:', err);
      setError('Failed to load portfolio data');
    } finally {
      setLoading(false);
    }
  };

  const handleItemAdded = () => {
    setShowAddModal(false);
    loadPortfolioData();
  };

  const handleItemRemoved = () => {
    loadPortfolioData();
  };

  // Calculate portfolio statistics
  const totalInvested = items.reduce((sum, item) => sum + (item.purchase_price * item.quantity), 0);
  const totalProfitLoss = totalValue - totalInvested;
  const totalProfitLossPct = totalInvested > 0 ? (totalProfitLoss / totalInvested) * 100 : 0;
  const profitableItems = items.filter(item => (item.profit_loss || 0) > 0).length;

  if (loading) {
    return (
      <div className="p-6 max-w-7xl mx-auto">
        <div className="text-center py-12">
          <LoadingSpinner size="large" className="mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400">Loading portfolio...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            Portfolio Tracker
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Track your investments and profits in {currentLeague}
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={loadPortfolioData}
            disabled={loading}
            className="btn-secondary flex items-center"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
          
          <button
            onClick={() => setShowAddModal(true)}
            className="btn-primary flex items-center"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Item
          </button>
        </div>
      </div>

      {/* Portfolio Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="card p-6 text-center">
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Total Value</p>
          <p className="text-3xl font-bold chaos-value" data-testid="total-value">
            {formatCurrency(totalValue, 'chaos')}
          </p>
        </div>
        
        <div className="card p-6 text-center">
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Total Invested</p>
          <p className="text-3xl font-bold text-gray-900 dark:text-gray-100">
            {formatCurrency(totalInvested, 'chaos')}
          </p>
        </div>
        
        <div className="card p-6 text-center">
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Profit/Loss</p>
          <div className="flex items-center justify-center space-x-2">
            {totalProfitLoss >= 0 ? (
              <TrendingUp className="w-5 h-5 text-green-500" />
            ) : (
              <TrendingDown className="w-5 h-5 text-red-500" />
            )}
            <p className={`text-3xl font-bold ${totalProfitLoss >= 0 ? 'profit-positive' : 'profit-negative'}`}>
              {totalProfitLoss >= 0 ? '+' : ''}{formatCurrency(totalProfitLoss, 'chaos')}
            </p>
          </div>
          <p className={`text-sm ${totalProfitLoss >= 0 ? 'profit-positive' : 'profit-negative'}`}>
            {totalProfitLoss >= 0 ? '+' : ''}{totalProfitLossPct.toFixed(1)}%
          </p>
        </div>
        
        <div className="card p-6 text-center">
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Items</p>
          <p className="text-3xl font-bold text-gray-900 dark:text-gray-100">
            {items.length}
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {profitableItems} profitable
          </p>
        </div>
      </div>

      {/* Error State */}
      {error && (
        <div className="card p-6 text-center">
          <div className="text-red-600 dark:text-red-400 text-4xl mb-4">⚠️</div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
            Portfolio Error
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
          <button onClick={loadPortfolioData} className="btn-primary">
            Retry
          </button>
        </div>
      )}

      {/* Empty State */}
      {!loading && !error && items.length === 0 && (
        <div className="card p-12 text-center">
          <Briefcase className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
            Your Portfolio is Empty
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Start tracking your Path of Exile investments by adding items to your portfolio.
          </p>
          <button
            onClick={() => setShowAddModal(true)}
            className="btn-primary flex items-center mx-auto"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Your First Item
          </button>
        </div>
      )}

      {/* Portfolio Items */}
      {items.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              Portfolio Items ({items.length})
            </h2>
            
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Sorted by profit/loss
            </div>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4" data-testid="portfolio-items">
            {items
              .sort((a, b) => (b.profit_loss || 0) - (a.profit_loss || 0))
              .map((item) => (
                <PortfolioItemCard
                  key={item.id}
                  item={item}
                  onRemove={handleItemRemoved}
                />
              ))}
          </div>
        </div>
      )}

      {/* Add Item Modal */}
      {showAddModal && (
        <AddPortfolioItemModal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          onItemAdded={handleItemAdded}
          league={currentLeague}
        />
      )}
    </div>
  );
};

export default Portfolio;
