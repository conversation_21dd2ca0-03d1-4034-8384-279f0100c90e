name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  CARGO_TERM_COLOR: always

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Rust
      uses: dtolnay/rust-toolchain@stable
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y libwebkit2gtk-4.0-dev libgtk-3-dev libayatana-appindicator3-dev librsvg2-dev
        
    - name: Install frontend dependencies
      run: npm ci
      
    - name: Run Rust tests
      run: cargo test --manifest-path=src-tauri/Cargo.toml
      
    - name: Run frontend tests
      run: npm test
      
    - name: Lint Rust code
      run: cargo clippy --manifest-path=src-tauri/Cargo.toml -- -D warnings
      
    - name: Lint frontend code
      run: npm run lint

  build:
    needs: test
    strategy:
      fail-fast: false
      matrix:
        platform: [macos-latest, ubuntu-20.04, windows-latest]
        
    runs-on: ${{ matrix.platform }}
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Rust
      uses: dtolnay/rust-toolchain@stable
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies (Ubuntu)
      if: matrix.platform == 'ubuntu-20.04'
      run: |
        sudo apt-get update
        sudo apt-get install -y libwebkit2gtk-4.0-dev libgtk-3-dev libayatana-appindicator3-dev librsvg2-dev
        
    - name: Install frontend dependencies
      run: npm ci
      
    - name: Build frontend
      run: npm run build
      
    - name: Build Tauri app
      run: npm run tauri:build
