import React, { useState, useRef } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { Paper, TextField, Button, Typography, Box, CircularProgress, Tooltip } from '@mui/material';
import { AutoFixHigh as MagicIcon } from '@mui/icons-material';

type Sender = 'user' | 'bot' | 'tool';

interface Message {
  text: string;
  sender: Sender;
  tool_call?: any;
  tool_response?: any;
}

const tools = [
  {
    function_declarations: [
      {
        name: 'query_database',
        description: 'Query the game database for items or prices',
        parameters: {
          type: 'object',
          properties: {
            table: {
              type: 'string',
              enum: ['items', 'prices'],
            },
            filters: {
              type: 'object',
              properties: {
                name: { type: 'string' },
                category: { type: 'string' },
                limit: { type: 'number' },
              },
            },
          },
          required: ['table'],
        },
      },
    ],
  },
];

export const GeminiChat: React.FC = () => {
  const [input, setInput] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const messagesEndRef = useRef<null | HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  React.useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSend = async () => {
    if (!input.trim()) return;

    const userMessage: Message = { text: input, sender: 'user' };
    const newMessages = [...messages, userMessage];
    setMessages(newMessages);
    setInput('');
    setIsLoading(true);
    setError(null);

    try {
      await processMessage(newMessages);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(`Failed to get response: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  const processMessage = async (currentMessages: Message[]) => {
    const history = currentMessages.map(m => ({
        role: m.sender === 'user' ? 'user' : 'model',
        parts: [{ text: m.text }]
    }));

    const response: any = await invoke('send_chat_message', {
      history,
      tools,
    });

    if (response.tool_calls && response.tool_calls.length > 0) {
      const toolCall = response.tool_calls[0];
      const functionCall = toolCall.function_call;
      
      const toolMessage: Message = {
        text: `Calling tool: ${functionCall.name} with args: ${JSON.stringify(functionCall.args)}`,
        sender: 'tool',
        tool_call: functionCall,
      };
      setMessages(prev => [...prev, toolMessage]);

      // Invoke the Tauri command
      const toolResponse: string = await invoke(functionCall.name, functionCall.args);
      
      const toolResponseMessage: Message = {
          text: `Tool response: ${toolResponse}`,
          sender: 'bot',
          tool_response: JSON.parse(toolResponse),
      };
      
      const messagesForNextTurn = [...currentMessages, toolMessage, toolResponseMessage];
      setMessages(messagesForNextTurn);
      
      // Send the tool response back to Gemini
      await processMessage(messagesForNextTurn);

    } else {
      const botMessage: Message = { text: response.text, sender: 'bot' };
      setMessages((prev) => [...prev, botMessage]);
    }
  }


  return (
    <Paper elevation={3} sx={{ p: 2, display: 'flex', flexDirection: 'column', height: '100%' }}>
      <Typography variant="h6" gutterBottom>
        Gemini Chat
      </Typography>
      <Box sx={{ flexGrow: 1, overflowY: 'auto', mb: 2, p: 1, border: '1px solid #ddd', borderRadius: 1 }}>
        {messages.map((msg, index) => (
          <Box
            key={index}
            sx={{
              mb: 1,
              p: 1,
              borderRadius: 1,
              bgcolor: msg.sender === 'user' ? 'primary.light' : (msg.sender === 'bot' ? 'secondary.light' : '#f0f0f0'),
              alignSelf: msg.sender === 'user' ? 'flex-end' : 'flex-start',
              maxWidth: '80%',
              ml: msg.sender === 'user' ? 'auto' : 0,
              mr: msg.sender === 'bot' || msg.sender === 'tool' ? 'auto' : 0,
            }}
          >
            {msg.sender === 'tool' && (
                <Tooltip title={`Calling tool: ${msg.tool_call?.name}\nArgs: ${JSON.stringify(msg.tool_call?.args, null, 2)}`}>
                    <Box sx={{display: 'flex', alignItems: 'center', color: 'grey.700'}}>
                        <MagicIcon fontSize="small" sx={{mr: 1}}/>
                        <Typography variant="caption" sx={{ fontStyle: 'italic' }}>
                            Thinking...
                        </Typography>
                    </Box>
                </Tooltip>
            )}
            <Typography variant="body1">{msg.text}</Typography>
          </Box>
        ))}
        <div ref={messagesEndRef} />
        {isLoading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
            <CircularProgress />
          </Box>
        )}
        {error && (
          <Typography color="error" sx={{ mt: 2 }}>
            {error}
          </Typography>
        )}
      </Box>
      <Box sx={{ display: 'flex' }}>
        <TextField
          fullWidth
          variant="outlined"
          size="small"
          value={input}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => setInput(e.target.value)}
          onKeyPress={(e: React.KeyboardEvent<HTMLInputElement>) => e.key === 'Enter' && handleSend()}
          placeholder="Ask something about items or prices..."
          disabled={isLoading}
        />
        <Button
          variant="contained"
          color="primary"
          onClick={handleSend}
          disabled={isLoading}
          sx={{ ml: 1 }}
        >
          Send
        </Button>
      </Box>
    </Paper>
  );
};