// Type definitions for PoE Profit AI

export interface League {
  id: number;
  name: string;
  display_name: string;
  is_active: boolean;
  first_seen: string;
  last_seen: string;
}

export interface Item {
  id: number;
  details_id: string;
  name: string;
  type_line?: string;
  base_type?: string;
  category: string;
  subcategory?: string;
  icon_url?: string;
  league_id: number;
}

export interface Price {
  id: number;
  item_id: number;
  league_id: number;
  chaos_value?: number;
  divine_value?: number;
  listing_count: number;
  volatility?: number;
  momentum_7d?: number;
  liquidity_score?: number;
  confidence_score: number;
  is_outlier: boolean;
  snapshot_time: string;
}

export interface PriceQuery {
  item_name: string;
  league?: string;
}

export interface PriceResult {
  item_name: string;
  chaos_value?: number;
  divine_value?: number;
  confidence: number;
  last_updated: string;
  trend_7d?: number;
  liquidity: string;
  listing_count: number;
}

export interface FlipFinderQuery {
  budget_chaos: number;
  min_profit_pct: number;
  max_risk_level: RiskLevel;
  league: string;
}

export interface FlipOpportunity {
  item_name: string;
  buy_price: number;
  sell_price: number;
  profit_chaos: number;
  profit_pct: number;
  risk_level: RiskLevel;
  confidence: number;
  liquidity_score: number;
  time_to_sell_estimate: string;
  listing_count: number;
}

export enum RiskLevel {
  Low = 'Low',
  Medium = 'Medium',
  High = 'High',
}

export interface PortfolioItem {
  id?: number;
  item_name: string;
  quantity: number;
  purchase_price: number;
  current_value?: number;
  profit_loss?: number;
  profit_loss_pct?: number;
  league: string;
  added_at: string;
}

export interface MarketSummary {
  total_items: number;
  active_leagues: number;
  last_update: string;
  top_movers: TopMover[];
  currency_rates: CurrencyRates;
}

export interface TopMover {
  item_name: string;
  change_pct: number;
  current_price: number;
  volume: number;
}

export interface CurrencyRates {
  chaos_to_divine: number;
  divine_to_chaos: number;
  exalt_to_chaos?: number;
}

export interface AppInfo {
  version: string;
  startup_time_ms: number;
  uptime_seconds: number;
  database_size_mb: number;
  cache_size: number;
}

export interface PerformanceMetrics {
  avg_query_time_ms: number;
  cache_hit_rate: number;
  memory_usage_mb: number;
  cpu_usage_percent: number;
  active_connections: number;
  last_updated: string;
}

export interface SyncStatus {
  is_syncing: boolean;
  last_sync?: string;
  last_error?: string;
  items_synced: number;
  sync_duration_ms: number;
  next_sync_eta?: string;
}

// UI State types
export interface AppState {
  currentLeague: string;
  theme: 'light' | 'dark';
  isLoading: boolean;
  error?: string;
}

export interface DashboardData {
  topFlips: FlipOpportunity[];
  portfolioValue: number;
  recentAlerts: Alert[];
  marketSummary: MarketSummary;
}

export interface Alert {
  id: string;
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
}

// Navigation types
export type NavigationTab = 'dashboard' | 'quick-price' | 'flip-finder' | 'portfolio' | 'league-lab' | 'settings';

// Form types
export interface AddPortfolioItemForm {
  item_name: string;
  quantity: number;
  purchase_price: number;
  league: string;
}

export interface FlipFinderForm {
  budget_chaos: number;
  min_profit_pct: number;
  max_risk_level: RiskLevel;
  league: string;
}
