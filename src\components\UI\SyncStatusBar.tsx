import React from 'react';
import { RefreshCw, CheckCircle, AlertCircle, Clock, WifiOff } from 'lucide-react';
import { useAppStore } from '../../stores/appStore';
import { formatTimeAgo } from '../../utils/tauri';
import { Toaster, toast } from 'sonner';

const SyncStatusBar: React.FC = () => {
  const { syncState, lastSync, errorMsg, syncProgress } = useAppStore((s) => ({
    syncState: s.syncState,
    lastSync: s.lastSync,
    errorMsg: s.errorMsg,
    syncProgress: s.syncProgress,
  }));

  React.useEffect(() => {
    if (syncState === 'error-timeout') {
      toast.error('Sync timed out. Please check your connection and try again.', {
        duration: 10000,
      });
    }
  }, [syncState]);


  const getStatusIcon = () => {
    switch (syncState) {
      case 'running':
        return <RefreshCw className="w-4 h-4 animate-spin" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'error-timeout':
        return <WifiOff className="w-4 h-4 text-red-500" />;
      case 'completed':
      case 'idle':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusText = () => {
    switch (syncState) {
      case 'running':
        if (isFinite(syncProgress)) {
          return `Syncing... ${syncProgress}%`;
        }
        return 'Syncing...';
      case 'error':
        return `Sync failed: ${errorMsg || 'Unknown error'}`;
      case 'error-timeout':
        return 'Sync timed out.';
      case 'completed':
        return `Sync complete. Last sync: ${lastSync ? formatTimeAgo(lastSync.toISOString()) : 'just now'}`;
      case 'idle':
        if (lastSync) {
          return `Last sync: ${formatTimeAgo(lastSync.toISOString())}`;
        }
        return 'Ready';
      default:
        return 'No sync data available';
    }
  };

  const getStatusColor = () => {
    switch (syncState) {
      case 'running':
        return 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300';
      case 'error':
      case 'error-timeout':
        return 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300';
      case 'completed':
      case 'idle':
        return 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300';
      default:
        return 'bg-gray-50 dark:bg-gray-800 text-gray-700 dark:text-gray-300';
    }
  };

  const isDeterminate = syncState === 'running' && isFinite(syncProgress);

  return (
    <>
      <Toaster richColors />
      <div className={`px-4 py-1 text-sm ${getStatusColor()} relative`} data-testid="sync-status">
        <div className="flex items-center justify-between max-w-7xl mx-auto">
          <div className="flex items-center space-x-2">
            {getStatusIcon()}
            <span data-testid="sync-status-text">{getStatusText()}</span>
          </div>
        </div>
        {syncState === 'running' && (
           <div className="absolute bottom-0 left-0 h-0.5 w-full bg-blue-200 dark:bg-blue-700">
             <div
               className={`h-full ${isDeterminate ? 'bg-blue-500' : 'animate-pulse bg-blue-400'}`}
               style={{ width: isDeterminate ? `${syncProgress}%` : '100%' }}
             />
           </div>
         )}
      </div>
    </>
  );
};

export default SyncStatusBar;
