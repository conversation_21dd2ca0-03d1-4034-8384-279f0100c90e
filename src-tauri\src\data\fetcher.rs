use reqwest::Client;
use std::time::Duration;
use tokio::time::sleep;
use anyhow::Result;
use governor::{Quota, RateLimiter, state::{direct::NotKeyed, InMemoryState}, clock::DefaultClock, middleware::NoOpMiddleware};
use std::num::NonZeroU32;

use crate::models::{PoeNinjaResponse, PoeNinjaItem};

pub struct PoeNinjaClient {
    client: Client,
    base_url: String,
    rate_limiter: RateLimiter<NotKeyed, InMemoryState, DefaultClock, NoOpMiddleware>,
}

#[derive(Debug, Clone)]
pub struct EndpointData {
    pub league: String,
    pub category: String,
    pub endpoint: String,
    pub data: PoeNinjaResponse,
}

impl PoeNinjaClient {
    pub fn new() -> Self {
        let client = Client::builder()
            .timeout(Duration::from_secs(30))
            .user_agent("PoE-Profit-AI/1.0")
            .build()
            .expect("Failed to create HTTP client");
        
        // Rate limiter: 45 requests per minute
        let quota = Quota::per_minute(NonZeroU32::new(45).unwrap());
        let rate_limiter = RateLimiter::direct(quota);
        
        Self {
            client,
            base_url: "https://poe.ninja/api/data".to_string(),
            rate_limiter,
        }
    }
    
    pub async fn fetch_all_data(&self, league: &str) -> Result<Vec<EndpointData>> {
        log::info!("Starting fetch_all_data for league: {}", league);

        let endpoints = vec![
            ("currencyoverview", "Currency"),
            ("currencyoverview", "Fragment"),
            ("itemoverview", "DivinationCard"),
            ("itemoverview", "Artifact"),
            ("itemoverview", "Oil"),
            ("itemoverview", "Incubator"),
            ("itemoverview", "UniqueWeapon"),
            ("itemoverview", "UniqueArmour"),
            ("itemoverview", "UniqueAccessory"),
            ("itemoverview", "UniqueFlask"),
            ("itemoverview", "UniqueJewel"),
            ("itemoverview", "SkillGem"),
            ("itemoverview", "ClusterJewel"),
            ("itemoverview", "Map"),
            ("itemoverview", "BlightedMap"),
            ("itemoverview", "BlightRavagedMap"),
            ("itemoverview", "ScourgedMap"),
            ("itemoverview", "UniqueMap"),
            ("itemoverview", "DeliriumOrb"),
            ("itemoverview", "Invitation"),
            ("itemoverview", "Scarab"),
            ("itemoverview", "BaseType"),
            ("itemoverview", "Fossil"),
            ("itemoverview", "Resonator"),
            ("itemoverview", "HelmetEnchant"),
            ("itemoverview", "Beast"),
            ("itemoverview", "Essence"),
            ("itemoverview", "Vial"),
        ];

        log::debug!("Fetching {} endpoints with rate limiting", endpoints.len());
        let mut results = Vec::new();
        let fetch_start = std::time::Instant::now();

        for (index, (category, endpoint)) in endpoints.iter().enumerate() {
            log::debug!("Fetching endpoint {}/{}: {} {}",
                index + 1, endpoints.len(), category, endpoint);

            let endpoint_start = std::time::Instant::now();
            match self.fetch_endpoint_data(league, category, endpoint).await {
                Ok(data) => {
                    let endpoint_duration = endpoint_start.elapsed();
                    let items_count = data.data.lines.len();
                    log::info!("Successfully fetched {}: {} items in {:?}",
                        endpoint, items_count, endpoint_duration);
                    log::debug!("Response details for {}: {} lines, currency_details: {}",
                        endpoint, items_count,
                        data.data.currency_details.as_ref().map_or(0, |cd| cd.len()));
                    results.push(data);
                }
                Err(e) => {
                    let endpoint_duration = endpoint_start.elapsed();
                    log::warn!("Failed to fetch {} {} after {:?}: {}",
                        category, endpoint, endpoint_duration, e);
                    log::debug!("Error details for {} {}: {}", category, endpoint, e);
                    // Continue with other endpoints
                }
            }

            // Small delay between requests
            log::debug!("Applying 100ms delay before next request...");
            sleep(Duration::from_millis(100)).await;
        }

        let total_duration = fetch_start.elapsed();
        let total_items: usize = results.iter().map(|r| r.data.lines.len()).sum();
        log::info!("Completed fetch_all_data: {}/{} endpoints successful, {} total items in {:?}",
            results.len(), endpoints.len(), total_items, total_duration);
        log::debug!("Average fetch rate: {:.1} items/sec",
            total_items as f64 / total_duration.as_secs_f64());

        Ok(results)
    }
    
    pub async fn fetch_endpoint_data(
        &self,
        league: &str,
        category: &str,
        endpoint: &str,
    ) -> Result<EndpointData> {
        log::debug!("Starting fetch_endpoint_data for {} {}", category, endpoint);

        // Wait for rate limiter
        log::debug!("Waiting for rate limiter...");
        self.rate_limiter.until_ready().await;

        let url = format!(
            "{}/{}?league={}&type={}",
            self.base_url, category, league, endpoint
        );

        log::debug!("Fetching URL: {}", url);

        // Retry logic with exponential backoff
        let mut attempt = 0;
        let max_attempts = 3;
        let request_start = std::time::Instant::now();

        loop {
            log::debug!("Attempt {}/{} for {} {}", attempt + 1, max_attempts, category, endpoint);

            match self.fetch_with_timeout(&url).await {
                Ok(response) => {
                    let status = response.status();
                    let headers = response.headers().clone();

                    log::debug!("HTTP {} for {} {} - Content-Length: {:?}",
                        status, category, endpoint,
                        headers.get("content-length"));

                    if status.is_success() {
                        let response_size = headers.get("content-length")
                            .and_then(|v| v.to_str().ok())
                            .and_then(|s| s.parse::<usize>().ok())
                            .unwrap_or(0);

                        let data: PoeNinjaResponse = response.json().await?;
                        let request_duration = request_start.elapsed();

                        log::debug!("Successfully parsed JSON for {} {}: {} items, {} bytes in {:?}",
                            category, endpoint, data.lines.len(), response_size, request_duration);

                        return Ok(EndpointData {
                            league: league.to_string(),
                            category: category.to_string(),
                            endpoint: endpoint.to_string(),
                            data,
                        });
                    } else if status.as_u16() == 429 {
                        // Rate limited - wait longer
                        let wait_time = Duration::from_secs(2_u64.pow(attempt + 2));
                        let retry_after = headers.get("retry-after")
                            .and_then(|v| v.to_str().ok())
                            .unwrap_or("unknown");
                        log::warn!("Rate limited for {} {}, waiting {:?} (Retry-After: {})",
                            category, endpoint, wait_time, retry_after);
                        sleep(wait_time).await;
                    } else {
                        let error_body = response.text().await.unwrap_or_default();
                        log::debug!("HTTP error {} for {} {}: {}",
                            status, category, endpoint,
                            if error_body.len() > 200 { &error_body[..200] } else { &error_body });
                        return Err(anyhow::anyhow!("HTTP error {}: {}", status, error_body));
                    }
                }
                Err(e) if attempt < max_attempts - 1 => {
                    let wait_time = Duration::from_millis(500 * 2_u64.pow(attempt));
                    log::warn!("Request failed for {} {} (attempt {}), retrying in {:?}: {}",
                        category, endpoint, attempt + 1, wait_time, e);
                    log::debug!("Error details for {} {}: {}", category, endpoint, e);
                    sleep(wait_time).await;
                }
                Err(e) => {
                    let total_duration = request_start.elapsed();
                    log::error!("All retry attempts failed for {} {} after {:?}: {}",
                        category, endpoint, total_duration, e);
                    return Err(anyhow::anyhow!("All retry attempts failed: {}", e));
                }
            }

            attempt += 1;
            if attempt >= max_attempts {
                let total_duration = request_start.elapsed();
                log::error!("Max retry attempts exceeded for {} {} after {:?}",
                    category, endpoint, total_duration);
                return Err(anyhow::anyhow!("Max retry attempts exceeded"));
            }
        }
    }
    
    async fn fetch_with_timeout(&self, url: &str) -> Result<reqwest::Response, reqwest::Error> {
        // For now, just use the client directly without timeout wrapper
        // The client already has a 30-second timeout configured
        self.client.get(url).send().await
    }
    
    pub async fn get_active_leagues(&self) -> Result<Vec<String>> {
        // For now, return hardcoded leagues
        // In a real implementation, you'd fetch this from the API
        Ok(vec![
            "Standard".to_string(),
            "Hardcore".to_string(),
            "Settlers".to_string(),
        ])
    }
}
