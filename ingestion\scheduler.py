"""
Data Acquisition Scheduler
=========================
Implements 31-minute polling cadence with offset to avoid thundering herd
Based on the comprehensive sync blueprint requirements
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Optional, Callable, Dict, Any
import random
from dataclasses import dataclass
from enum import Enum

from poe_ninja_api import PoeNinjaAP<PERSON>
from ingestion.fetch_task import FetchTask
from ingestion.validator import DataValidator
from ingestion.etl import ETLPipeline

logger = logging.getLogger(__name__)

class SchedulerState(Enum):
    STOPPED = "stopped"
    RUNNING = "running"
    PAUSED = "paused"
    ERROR = "error"

@dataclass
class ScheduleConfig:
    """Configuration for the scheduler"""
    # 31 minutes as per blueprint (30 min cache + 1 min offset)
    poll_interval_minutes: int = 31
    
    # Offset by +1 min past the half-hour to avoid thundering herd
    offset_minutes: int = 1
    
    # Jitter range to spread requests (±5 seconds)
    jitter_seconds: int = 5
    
    # Maximum consecutive failures before backing off
    max_consecutive_failures: int = 3
    
    # Backoff interval when failures exceed threshold (60 min)
    backoff_interval_minutes: int = 60
    
    # Auto-restart after backoff
    auto_restart: bool = True

class DataAcquisitionScheduler:
    """
    Production scheduler for PoE.ninja data acquisition
    
    Features:
    - 31-minute polling cadence with +1 min offset
    - Jittered start times to avoid thundering herd
    - Automatic backoff on consecutive failures
    - Health monitoring and metrics collection
    - Graceful shutdown handling
    """
    
    def __init__(self, 
                 config: ScheduleConfig = None,
                 league: str = "Mercenaries",
                 database_url: str = None):
        self.config = config or ScheduleConfig()
        self.league = league
        self.database_url = database_url
        
        self.state = SchedulerState.STOPPED
        self.consecutive_failures = 0
        self.last_successful_run: Optional[datetime] = None
        self.last_error: Optional[str] = None
        self.total_runs = 0
        self.total_failures = 0
        
        # Components
        self.api_client: Optional[PoeNinjaAPI] = None
        self.fetch_task: Optional[FetchTask] = None
        self.validator: Optional[DataValidator] = None
        self.etl_pipeline: Optional[ETLPipeline] = None
        
        # Task management
        self._scheduler_task: Optional[asyncio.Task] = None
        self._shutdown_event = asyncio.Event()
        
        # Callbacks for monitoring
        self.on_success: Optional[Callable[[Dict[str, Any]], None]] = None
        self.on_failure: Optional[Callable[[str], None]] = None
        self.on_state_change: Optional[Callable[[SchedulerState], None]] = None
        
    async def initialize(self):
        """Initialize all components"""
        logger.info("Initializing data acquisition scheduler")
        
        try:
            # Initialize API client
            self.api_client = PoeNinjaAPI(league=self.league)
            
            # Initialize components
            self.fetch_task = FetchTask(self.api_client, self.database_url)
            self.validator = DataValidator()
            self.etl_pipeline = ETLPipeline(self.database_url)
            
            await self.fetch_task.initialize()
            await self.etl_pipeline.initialize()
            
            logger.info("Scheduler initialization complete")
            
        except Exception as e:
            logger.error(f"Failed to initialize scheduler: {e}")
            raise
            
    async def start(self):
        """Start the scheduler"""
        if self.state == SchedulerState.RUNNING:
            logger.warning("Scheduler is already running")
            return
            
        if not self.api_client:
            await self.initialize()
            
        logger.info(f"Starting scheduler with {self.config.poll_interval_minutes}min interval")
        
        self.state = SchedulerState.RUNNING
        self._notify_state_change()
        
        # Start the main scheduler loop
        self._scheduler_task = asyncio.create_task(self._scheduler_loop())
        
    async def stop(self):
        """Stop the scheduler gracefully"""
        logger.info("Stopping scheduler...")
        
        self.state = SchedulerState.STOPPED
        self._notify_state_change()
        
        # Signal shutdown
        self._shutdown_event.set()
        
        # Wait for scheduler task to complete
        if self._scheduler_task:
            try:
                await asyncio.wait_for(self._scheduler_task, timeout=30)
            except asyncio.TimeoutError:
                logger.warning("Scheduler task did not stop gracefully, cancelling")
                self._scheduler_task.cancel()
                
        # Cleanup components
        if self.api_client:
            await self.api_client.__aexit__(None, None, None)
            
        logger.info("Scheduler stopped")
        
    async def pause(self):
        """Pause the scheduler"""
        if self.state == SchedulerState.RUNNING:
            self.state = SchedulerState.PAUSED
            self._notify_state_change()
            logger.info("Scheduler paused")
            
    async def resume(self):
        """Resume the scheduler"""
        if self.state == SchedulerState.PAUSED:
            self.state = SchedulerState.RUNNING
            self._notify_state_change()
            logger.info("Scheduler resumed")
            
    def _calculate_next_run_time(self) -> datetime:
        """
        Calculate next run time with proper offset and jitter
        
        Blueprint requirement: offset by +1 min past the half-hour
        to avoid thundering herd at :00 and :30
        """
        now = datetime.now()
        
        # Calculate next 31-minute interval
        next_run = now + timedelta(minutes=self.config.poll_interval_minutes)
        
        # Apply offset (+1 min past half-hour)
        minutes_past_hour = next_run.minute
        if minutes_past_hour < 31:
            # Next run should be at :31
            next_run = next_run.replace(minute=31, second=0, microsecond=0)
        else:
            # Next run should be at :01 of next hour
            next_run = next_run.replace(minute=1, second=0, microsecond=0)
            next_run += timedelta(hours=1)
            
        # Add jitter (±5 seconds)
        jitter = random.uniform(-self.config.jitter_seconds, self.config.jitter_seconds)
        next_run += timedelta(seconds=jitter)
        
        return next_run
        
    async def _scheduler_loop(self):
        """Main scheduler loop"""
        logger.info("Scheduler loop started")
        
        try:
            while not self._shutdown_event.is_set():
                if self.state == SchedulerState.RUNNING:
                    await self._execute_data_acquisition()
                    
                # Calculate next run time
                next_run = self._calculate_next_run_time()
                wait_seconds = (next_run - datetime.now()).total_seconds()
                
                if wait_seconds > 0:
                    logger.info(f"Next data acquisition scheduled for {next_run}")
                    
                    # Wait with periodic checks for shutdown/pause
                    while wait_seconds > 0 and not self._shutdown_event.is_set():
                        check_interval = min(60, wait_seconds)  # Check every minute
                        
                        try:
                            await asyncio.wait_for(
                                self._shutdown_event.wait(), 
                                timeout=check_interval
                            )
                            break  # Shutdown requested
                        except asyncio.TimeoutError:
                            wait_seconds -= check_interval
                            
                            # Handle pause state
                            while (self.state == SchedulerState.PAUSED and 
                                   not self._shutdown_event.is_set()):
                                await asyncio.sleep(10)
                                
        except Exception as e:
            logger.error(f"Scheduler loop error: {e}")
            self.state = SchedulerState.ERROR
            self.last_error = str(e)
            self._notify_state_change()
            
        logger.info("Scheduler loop ended")
        
    async def _execute_data_acquisition(self):
        """Execute a single data acquisition cycle"""
        start_time = datetime.now()
        self.total_runs += 1
        
        logger.info(f"Starting data acquisition cycle #{self.total_runs}")
        
        try:
            # Check if we should back off due to consecutive failures
            if (self.consecutive_failures >= self.config.max_consecutive_failures and
                self.config.auto_restart):
                logger.warning(f"Backing off due to {self.consecutive_failures} consecutive failures")
                await asyncio.sleep(self.config.backoff_interval_minutes * 60)
                
            # Execute the data acquisition pipeline
            async with self.api_client:
                # Step 1: Fetch all data
                raw_data = await self.fetch_task.fetch_all_data()
                
                if not raw_data:
                    raise Exception("No data fetched from any endpoint")
                    
                # Step 2: Validate data
                validation_results = await self.validator.validate_all(raw_data)
                
                # Step 3: Process through ETL pipeline
                etl_results = await self.etl_pipeline.process_batch(
                    raw_data, validation_results
                )
                
                # Success!
                self.consecutive_failures = 0
                self.last_successful_run = start_time
                
                duration = (datetime.now() - start_time).total_seconds()
                
                success_info = {
                    'duration_seconds': duration,
                    'endpoints_processed': len(raw_data),
                    'total_rows': sum(len(data.get('lines', [])) for data in raw_data.values() if data),
                    'validation_results': validation_results,
                    'etl_results': etl_results
                }
                
                logger.info(f"Data acquisition completed successfully in {duration:.1f}s")
                
                if self.on_success:
                    self.on_success(success_info)
                    
        except Exception as e:
            self.consecutive_failures += 1
            self.total_failures += 1
            self.last_error = str(e)
            
            duration = (datetime.now() - start_time).total_seconds()
            
            logger.error(f"Data acquisition failed after {duration:.1f}s: {e}")
            logger.error(f"Consecutive failures: {self.consecutive_failures}")
            
            if self.on_failure:
                self.on_failure(str(e))
                
            # Set error state if too many consecutive failures
            if self.consecutive_failures >= self.config.max_consecutive_failures:
                self.state = SchedulerState.ERROR
                self._notify_state_change()
                
    def _notify_state_change(self):
        """Notify listeners of state changes"""
        if self.on_state_change:
            self.on_state_change(self.state)
            
    def get_status(self) -> Dict[str, Any]:
        """Get current scheduler status"""
        return {
            'state': self.state.value,
            'league': self.league,
            'total_runs': self.total_runs,
            'total_failures': self.total_failures,
            'consecutive_failures': self.consecutive_failures,
            'last_successful_run': self.last_successful_run.isoformat() if self.last_successful_run else None,
            'last_error': self.last_error,
            'next_run': self._calculate_next_run_time().isoformat() if self.state == SchedulerState.RUNNING else None,
            'config': {
                'poll_interval_minutes': self.config.poll_interval_minutes,
                'offset_minutes': self.config.offset_minutes,
                'jitter_seconds': self.config.jitter_seconds
            }
        }
