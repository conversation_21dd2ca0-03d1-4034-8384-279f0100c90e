#!/usr/bin/env python3
"""
PoE Dashboard Data Sync Script
=============================
Standalone script for running data synchronization from Tauri app
"""

import asyncio
import logging
import sys
import argparse
from datetime import datetime
from typing import Optional

from poe_ninja_api import PoeNinjaAPI, EndpointType
from ingestion.fetch_task import FetchTask
from ingestion.etl import ETLPipeline
from ingestion.validator import DataValidator
from database.connection import DatabaseConnection
from config.settings import get_settings

# Configure logging with DEBUG level for detailed sync logs
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('logs/sync.log')
    ]
)

# Reduce noise from external libraries
logging.getLogger("asyncpg").setLevel(logging.WARNING)
logging.getLogger("aiohttp").setLevel(logging.WARNING)
logging.getLogger("urllib3").setLevel(logging.WARNING)
logger = logging.getLogger(__name__)

async def run_data_sync(league: str = "Standard") -> int:
    """
    Run a complete data synchronization cycle
    Returns the number of items synced
    """
    settings = get_settings()
    total_items_synced = 0

    try:
        logger.info(f"Starting data sync for league: {league}")
        logger.debug(f"Database URL: {settings.get_database_url()}")
        logger.debug(f"Environment: {settings.environment}")
        start_time = datetime.now()

        # Initialize components
        logger.debug("Initializing API client...")
        api_client = PoeNinjaAPI(league=league)

        logger.debug("Initializing database connection...")
        db_connection = DatabaseConnection(settings.get_database_url())

        logger.debug("Initializing fetch task...")
        fetch_task = FetchTask(api_client, settings.get_database_url())

        logger.debug("Initializing ETL pipeline...")
        etl_pipeline = ETLPipeline(settings.get_database_url())

        logger.debug("Initializing data validator...")
        validator = DataValidator()

        # Connect to database
        logger.debug("Connecting to database...")
        await db_connection.connect()
        logger.info("Database connection established")

        # Initialize API client
        logger.debug("Initializing API client session...")
        await api_client.initialize()
        logger.info("API client initialized")

        # Fetch all data from poe.ninja
        logger.info("Fetching data from poe.ninja API...")
        logger.debug("Starting parallel fetch of all endpoints...")
        fetch_start_time = datetime.now()
        raw_data = await fetch_task.fetch_all_data()
        fetch_duration = (datetime.now() - fetch_start_time).total_seconds()

        if not raw_data:
            logger.error("No data fetched from API")
            return 0

        logger.info(f"Fetched data from {len(raw_data)} endpoints in {fetch_duration:.2f}s")

        # Debug log endpoint details
        for endpoint, data in raw_data.items():
            if data and 'lines' in data:
                logger.debug(f"Endpoint {endpoint}: {len(data['lines'])} items")
            else:
                logger.debug(f"Endpoint {endpoint}: No data or empty response")
        
        # Validate data
        logger.info("Validating data quality...")
        logger.debug("Starting data validation for all endpoints...")
        validation_start_time = datetime.now()
        validation_results = await validator.validate_all(raw_data)
        validation_duration = (datetime.now() - validation_start_time).total_seconds()

        logger.debug(f"Data validation completed in {validation_duration:.2f}s")
        for endpoint, result in validation_results.items():
            if result:
                logger.debug(f"Validation {endpoint}: {result.is_valid} "
                           f"(errors: {len(result.errors)}, warnings: {len(result.warnings)})")

        # Process through ETL pipeline
        logger.info("Processing data through ETL pipeline...")
        logger.debug("Starting ETL batch processing...")
        etl_start_time = datetime.now()
        etl_results = await etl_pipeline.process_batch(raw_data, validation_results)
        etl_duration = (datetime.now() - etl_start_time).total_seconds()

        logger.debug(f"ETL processing completed in {etl_duration:.2f}s")

        # Count total items processed
        successful_etl = 0
        failed_etl = 0
        for result in etl_results:
            if result.success:
                total_items_synced += result.items_processed
                successful_etl += 1
                logger.debug(f"ETL success for {result.endpoint}: {result.items_processed} items")
            else:
                failed_etl += 1
                logger.debug(f"ETL failed for {result.endpoint}: {result.error}")

        logger.debug(f"ETL results: {successful_etl} successful, {failed_etl} failed")

        # Cleanup old data (keep 14 days)
        logger.info("Cleaning up old data...")
        logger.debug("Deleting data older than 14 days...")
        cleanup_start_time = datetime.now()
        cleanup_result = await db_connection.execute(
            "DELETE FROM prices WHERE snapshot_time < NOW() - INTERVAL '14 days'"
        )
        cleanup_duration = (datetime.now() - cleanup_start_time).total_seconds()
        logger.debug(f"Cleanup completed in {cleanup_duration:.2f}s, rows affected: {cleanup_result}")

        # Update sync statistics
        logger.debug("Updating sync statistics...")
        await db_connection.execute(
            """
            INSERT INTO stats_fetch (endpoint, league_name, status, rows_ingested, fetch_time)
            VALUES ('sync_complete', $1, 200, $2, NOW())
            """,
            league, total_items_synced
        )

        duration = (datetime.now() - start_time).total_seconds()
        logger.info(f"Data sync completed successfully")
        logger.info(f"Synced: {total_items_synced} items in {duration:.1f} seconds")
        logger.debug(f"Sync breakdown - Fetch: {fetch_duration:.2f}s, "
                   f"Validation: {validation_duration:.2f}s, "
                   f"ETL: {etl_duration:.2f}s, "
                   f"Cleanup: {cleanup_duration:.2f}s")

        # Print sync result for Tauri to parse
        print(f"Synced: {total_items_synced} items")

        return total_items_synced
        
    except Exception as e:
        logger.error(f"Data sync failed: {e}")
        print(f"Error: {e}")
        return 0
        
    finally:
        # Cleanup
        if 'api_client' in locals():
            await api_client.close()
        if 'db_connection' in locals():
            await db_connection.close()

async def test_api_connection(league: str = "Standard") -> bool:
    """Test API connection without full sync"""
    try:
        logger.info(f"Testing API connection for league: {league}")
        
        api_client = PoeNinjaAPI(league=league)
        await api_client.initialize()
        
        # Test a simple endpoint
        currency_data = await api_client.get_currency_data()
        
        if currency_data and 'lines' in currency_data:
            items_count = len(currency_data['lines'])
            logger.info(f"API test successful: {items_count} currency items found")
            print(f"API test successful: {items_count} items")
            return True
        else:
            logger.error("API test failed: No data returned")
            print("API test failed: No data returned")
            return False
            
    except Exception as e:
        logger.error(f"API test failed: {e}")
        print(f"API test failed: {e}")
        return False
        
    finally:
        if 'api_client' in locals():
            await api_client.close()

async def get_sync_status() -> dict:
    """Get current sync status from database"""
    try:
        settings = get_settings()
        db_connection = DatabaseConnection(settings.get_database_url())
        await db_connection.connect()
        
        # Get latest sync info
        result = await db_connection.fetch_one(
            """
            SELECT 
                COUNT(*) as items_synced,
                MAX(fetch_time) as last_sync,
                AVG(latency_ms) as avg_latency
            FROM stats_fetch 
            WHERE fetch_time > NOW() - INTERVAL '1 hour'
            """
        )
        
        status = {
            "is_syncing": False,  # Would need to check a lock table
            "last_sync": result['last_sync'].isoformat() if result['last_sync'] else None,
            "items_synced": result['items_synced'] or 0,
            "avg_latency_ms": float(result['avg_latency']) if result['avg_latency'] else 0,
        }
        
        print(f"Sync status: {status}")
        return status
        
    except Exception as e:
        logger.error(f"Failed to get sync status: {e}")
        print(f"Error getting sync status: {e}")
        return {}
        
    finally:
        if 'db_connection' in locals():
            await db_connection.close()

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='PoE Dashboard Data Sync')
    parser.add_argument('--sync', action='store_true', help='Run full data sync')
    parser.add_argument('--test', action='store_true', help='Test API connection')
    parser.add_argument('--status', action='store_true', help='Get sync status')
    parser.add_argument('--league', default='Standard', help='League to sync (default: Standard)')
    
    args = parser.parse_args()
    
    if args.sync:
        result = asyncio.run(run_data_sync(args.league))
        sys.exit(0 if result > 0 else 1)
    elif args.test:
        result = asyncio.run(test_api_connection(args.league))
        sys.exit(0 if result else 1)
    elif args.status:
        asyncio.run(get_sync_status())
        sys.exit(0)
    else:
        parser.print_help()
        sys.exit(1)

if __name__ == "__main__":
    main()
