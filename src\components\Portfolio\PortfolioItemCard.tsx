import React, { useState } from 'react';
import { TrendingUp, TrendingDown, Trash2, Calendar, DollarSign } from 'lucide-react';
import type { PortfolioItem } from '../../types';
import { removePortfolioItem, formatCurrency, formatTimeAgo } from '../../utils/tauri';

interface PortfolioItemCardProps {
  item: PortfolioItem;
  onRemove: () => void;
}

const PortfolioItemCard: React.FC<PortfolioItemCardProps> = ({ item, onRemove }) => {
  const [removing, setRemoving] = useState(false);

  const handleRemove = async () => {
    if (!item.id) return;
    
    if (!confirm(`Are you sure you want to remove ${item.item_name} from your portfolio?`)) {
      return;
    }
    
    try {
      setRemoving(true);
      await removePortfolioItem(item.id);
      onRemove();
    } catch (error) {
      console.error('Failed to remove item:', error);
      alert('Failed to remove item. Please try again.');
    } finally {
      setRemoving(false);
    }
  };

  const isProfit = (item.profit_loss || 0) >= 0;
  const currentValue = item.current_value ? item.current_value * item.quantity : null;
  const totalInvested = item.purchase_price * item.quantity;

  return (
    <div className="card p-6 hover:shadow-lg transition-shadow" data-testid="portfolio-item">
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div>
          <h3 className="font-semibold text-lg text-gray-900 dark:text-gray-100">
            {item.item_name}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Quantity: {item.quantity}
          </p>
        </div>
        
        <button
          onClick={handleRemove}
          disabled={removing}
          className="p-2 text-gray-400 hover:text-red-500 transition-colors disabled:opacity-50"
          title="Remove from portfolio"
        >
          <Trash2 className="w-4 h-4" />
        </button>
      </div>

      {/* Price Information */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <p className="text-xs text-gray-600 dark:text-gray-400 mb-1">Purchase Price</p>
          <p className="font-semibold text-gray-900 dark:text-gray-100">
            {formatCurrency(item.purchase_price, 'chaos')}
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            per item
          </p>
        </div>
        
        <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <p className="text-xs text-gray-600 dark:text-gray-400 mb-1">Current Price</p>
          <p className="font-semibold text-gray-900 dark:text-gray-100">
            {item.current_value ? formatCurrency(item.current_value, 'chaos') : 'N/A'}
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            per item
          </p>
        </div>
      </div>

      {/* Total Values */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div>
          <p className="text-sm text-gray-600 dark:text-gray-400">Total Invested</p>
          <p className="text-xl font-bold text-gray-900 dark:text-gray-100">
            {formatCurrency(totalInvested, 'chaos')}
          </p>
        </div>
        
        <div>
          <p className="text-sm text-gray-600 dark:text-gray-400">Current Value</p>
          <p className="text-xl font-bold text-gray-900 dark:text-gray-100">
            {currentValue ? formatCurrency(currentValue, 'chaos') : 'N/A'}
          </p>
        </div>
      </div>

      {/* Profit/Loss */}
      {item.profit_loss !== null && item.profit_loss !== undefined && (
        <div className={`p-3 rounded-lg mb-4 ${
          isProfit 
            ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800' 
            : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'
        }`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {isProfit ? (
                <TrendingUp className="w-5 h-5 text-green-600" />
              ) : (
                <TrendingDown className="w-5 h-5 text-red-600" />
              )}
              <span className="font-medium text-gray-900 dark:text-gray-100">
                Profit/Loss
              </span>
            </div>
            
            <div className="text-right">
              <p className={`text-lg font-bold ${isProfit ? 'profit-positive' : 'profit-negative'}`}>
                {isProfit ? '+' : ''}{formatCurrency(item.profit_loss, 'chaos')}
              </p>
              {item.profit_loss_pct !== null && item.profit_loss_pct !== undefined && (
                <p className={`text-sm ${isProfit ? 'profit-positive' : 'profit-negative'}`}>
                  {isProfit ? '+' : ''}{item.profit_loss_pct.toFixed(1)}%
                </p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Footer */}
      <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 pt-3 border-t border-gray-200 dark:border-gray-600">
        <div className="flex items-center">
          <Calendar className="w-4 h-4 mr-1" />
          <span>Added {formatTimeAgo(item.added_at)}</span>
        </div>
        
        <div className="flex items-center">
          <DollarSign className="w-4 h-4 mr-1" />
          <span>{item.league}</span>
        </div>
      </div>

      {/* No current price warning */}
      {!item.current_value && (
        <div className="mt-3 p-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded text-xs text-yellow-800 dark:text-yellow-300">
          ⚠️ Current price unavailable - item may not be actively traded
        </div>
      )}
    </div>
  );
};

export default PortfolioItemCard;
