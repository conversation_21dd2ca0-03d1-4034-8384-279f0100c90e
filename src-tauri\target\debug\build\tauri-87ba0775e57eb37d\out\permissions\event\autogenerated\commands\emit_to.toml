# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-emit-to"
description = "Enables the emit_to command without any pre-configured scope."
commands.allow = ["emit_to"]

[[permission]]
identifier = "deny-emit-to"
description = "Denies the emit_to command without any pre-configured scope."
commands.deny = ["emit_to"]
