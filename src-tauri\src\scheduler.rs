use tokio_cron_scheduler::{JobScheduler, Job};
use std::sync::Arc;
use chrono::{DateTime, Utc};
use tauri::{A<PERSON><PERSON>and<PERSON>, Emitter, Manager};

use crate::data::database::DatabaseManager;
use crate::data::fetcher::PoeNinjaClient;
use crate::models::{SyncStatus, PoeNinjaItem};

pub struct DataSyncScheduler {
    scheduler: JobScheduler,
    db_manager: Arc<DatabaseManager>,
    poe_client: Arc<PoeNinjaClient>,
    app_handle: AppHandle,
    last_sync: Option<DateTime<Utc>>,
    is_syncing: bool,
    last_error: Option<String>,
    items_synced: u64,
}

impl DataSyncScheduler {
    pub async fn new(
        db_manager: Arc<DatabaseManager>,
        poe_client: Arc<PoeNinjaClient>,
        app_handle: AppHandle,
    ) -> anyhow::Result<Self> {
        let scheduler = JobScheduler::new().await?;

        Ok(Self {
            scheduler,
            db_manager,
            poe_client,
            app_handle,
            last_sync: None,
            is_syncing: false,
            last_error: None,
            items_synced: 0,
        })
    }
    
    pub async fn start(&self) -> anyhow::Result<()> {
        let app_handle = self.app_handle.clone();
        let db_manager = self.db_manager.clone();
        let poe_client = self.poe_client.clone();

        // Schedule data sync every 31 minutes (your preferred cadence)
        let sync_job = Job::new_async("0 */31 * * * *", move |_uuid, _l| {
            let app_handle = app_handle.clone();
            let db_manager = db_manager.clone();
            let poe_client = poe_client.clone();

            Box::pin(async move {
                log::info!("Starting scheduled data sync...");

                // Emit sync started event
                if let Err(e) = app_handle.emit("sync-started", ()) {
                    log::error!("Failed to emit sync-started event: {}", e);
                }

                match run_rust_data_sync(&db_manager, &poe_client, &app_handle).await {
                    Ok(items_synced) => {
                        log::info!("Data sync completed successfully: {} items", items_synced);
                        if let Err(e) = app_handle.emit("sync-completed", items_synced) {
                            log::error!("Failed to emit sync-completed event: {}", e);
                        }
                    }
                    Err(e) => {
                        log::error!("Data sync failed: {}", e);
                        if let Err(e) = app_handle.emit("sync-error", e.to_string()) {
                            log::error!("Failed to emit sync-error event: {}", e);
                        }
                    }
                }
            })
        })?;

        self.scheduler.add(sync_job).await?;
        self.scheduler.start().await?;

        log::info!("Data sync scheduler started with 31-minute interval");
        Ok(())
    }
    
    pub async fn trigger_manual_sync(&mut self) -> anyhow::Result<u64> {
        log::info!("Manual data sync triggered");

        self.is_syncing = true;
        self.last_error = None;

        // Emit sync started event
        if let Err(e) = self.app_handle.emit("sync-started", ()) {
            log::error!("Failed to emit sync-started event: {}", e);
        }

        match run_rust_data_sync(&self.db_manager, &self.poe_client, &self.app_handle).await {
            Ok(items_synced) => {
                log::info!("Manual sync completed: {} items", items_synced);
                self.last_sync = Some(Utc::now());
                self.items_synced = items_synced;
                self.is_syncing = false;
                if let Err(e) = self.app_handle.emit("sync-completed", items_synced) {
                    log::error!("Failed to emit sync-completed event: {}", e);
                }
                Ok(items_synced)
            }
            Err(e) => {
                log::error!("Manual sync failed: {}", e);
                self.last_error = Some(e.to_string());
                self.is_syncing = false;
                if let Err(e) = self.app_handle.emit("sync-error", e.to_string()) {
                    log::error!("Failed to emit sync-error event: {}", e);
                }
                Err(e)
            }
        }
    }

    pub fn get_sync_status(&self) -> SyncStatus {
        SyncStatus {
            is_syncing: self.is_syncing,
            last_sync: self.last_sync,
            last_error: self.last_error.clone(),
            items_synced: self.items_synced as u32,
            sync_duration_ms: 5000, // Placeholder
            next_sync_eta: self.last_sync.map(|last| last + chrono::Duration::minutes(31)),
        }
    }
}

// Function to run Rust-based data sync using shared components
async fn run_rust_data_sync(
    db_manager: &DatabaseManager,
    poe_client: &PoeNinjaClient,
    app_handle: &AppHandle,
) -> anyhow::Result<u64> {
    log::info!("Starting Rust-based data sync...");
    let window = app_handle.webview_windows().get("main").ok_or_else(|| anyhow::anyhow!("Main window not found"))?.clone();


    let mut total_items_synced = 0u64;

    // Use Settlers as the current active league
    let league = "Settlers";
    log::debug!("Sync configuration - League: {}, Database: SQLite", league);

    // Ensure league exists in database
    log::debug!("Upserting league: {}", league);
    let league_id = db_manager.upsert_league(league, league).await
        .map_err(|e| anyhow::anyhow!("Failed to upsert league: {}", e))?;
    log::debug!("League upserted with ID: {}", league_id);

    // Use the existing fetch_all_data method which handles all categories
    log::debug!("Starting fetch from poe.ninja API...");
    let fetch_start = std::time::Instant::now();
    match poe_client.fetch_all_data(league, &window).await {
        Ok(endpoint_data_list) => {
            let fetch_duration = fetch_start.elapsed();
            log::info!("Fetched data from {} endpoints in {:?}", endpoint_data_list.len(), fetch_duration);
            log::debug!("Endpoints fetched: {:?}",
                endpoint_data_list.iter().map(|d| &d.endpoint).collect::<Vec<_>>());

            // Process each endpoint's data
            for endpoint_data in endpoint_data_list {
                log::info!("Processing {} items from {} category",
                          endpoint_data.data.lines.len(),
                          endpoint_data.category);
                log::debug!("Endpoint details - Category: {}, Endpoint: {}, League: {}",
                    endpoint_data.category, endpoint_data.endpoint, endpoint_data.league);

                let endpoint_start = std::time::Instant::now();
                let mut items_processed = 0u64;
                let mut items_failed = 0u64;

                // Prepare data for batch processing
                let items_data: Vec<(PoeNinjaItem, String)> = endpoint_data.data.lines
                    .iter()
                    .map(|item| (item.clone(), endpoint_data.category.clone()))
                    .collect();

                log::debug!("Starting batch upsert for {} items in category {}",
                    items_data.len(), endpoint_data.category);

                // Use optimized batch operation for much better performance (20-50x faster)
                // Try the subquery approach first (fastest), fallback to regular batch if needed
                let batch_result = if items_data.len() > 50 {
                    // For larger batches, use subquery approach (eliminates RETURNING round-trip)
                    db_manager.batch_upsert_items_with_prices_subquery(&items_data, league_id).await
                } else {
                    // For smaller batches, use regular batch approach
                    db_manager.batch_upsert_items_with_prices(&items_data, league_id).await
                };

                match batch_result {
                    Ok(processed) => {
                        items_processed = processed as u64;
                        total_items_synced += processed as u64;
                        log::debug!("Successfully batch processed {} items for {}",
                            processed, endpoint_data.category);
                    }
                    Err(e) => {
                        log::error!("Failed to batch process items for category {}: {}",
                            endpoint_data.category, e);
                        log::debug!("Batch processing error details: {}", e);
                        items_failed = items_data.len() as u64;
                    }
                }

                let endpoint_duration = endpoint_start.elapsed();
                log::info!("Completed {} category: {}/{} items processed in {:?}",
                    endpoint_data.category, items_processed,
                    endpoint_data.data.lines.len(), endpoint_duration);
                log::debug!("Processing rate for {}: {:.1} items/sec",
                    endpoint_data.category,
                    items_processed as f64 / endpoint_duration.as_secs_f64());

                if items_failed > 0 {
                    log::warn!("Failed to process {} items in {}", items_failed, endpoint_data.category);
                }

                // Small delay between processing endpoints
                log::debug!("Applying 100ms delay before next endpoint...");
                tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
            }
        }
        Err(e) => {
            log::error!("Failed to fetch data from poe.ninja: {}", e);
            return Err(anyhow::anyhow!("Failed to fetch data: {}", e));
        }
    }

    log::info!("Rust data sync completed: {} items synced", total_items_synced);
    Ok(total_items_synced)
}


