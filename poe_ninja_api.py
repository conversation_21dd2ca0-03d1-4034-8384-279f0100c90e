"""
PoE Ninja API Integration Module
===============================
Production-ready data acquisition system for Path of Exile economy data
Implements comprehensive sync blueprint with robust error handling and monitoring
"""

import asyncio
import aiohttp
import json
import time
import logging
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Union, Tuple
from dataclasses import dataclass
from enum import Enum
import random

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EndpointType(Enum):
    CURRENCY = "Currency"
    FRAGMENT = "Fragment"
    UNIQUE_WEAPON = "UniqueWeapon"
    UNIQUE_ARMOUR = "UniqueArmour"
    UNIQUE_ACCESSORY = "UniqueAccessory"
    UNIQUE_FLASK = "UniqueFlask"
    UNIQUE_JEWEL = "UniqueJewel"
    DIVINATION_CARD = "DivinationCard"
    SKILL_GEM = "SkillGem"
    BASE_TYPE = "BaseType"
    OIL = "Oil"
    INCUBATOR = "Incubator"
    SCARAB = "Scarab"
    FOSSIL = "Fossil"
    RESONATOR = "Resonator"
    ESSENCE = "Essence"
    BEAST = "Beast"
    VIAL = "Vial"
    DELIRIUM_ORB = "DeliriumOrb"
    OMEN = "Omen"
    UNIQUE_RELIC = "UniqueRelic"
    CLUSTER_JEWEL = "ClusterJewel"
    BLIGHTED_MAP = "BlightedMap"
    BLIGHT_RAVAGED_MAP = "BlightRavagedMap"
    INVITATION = "Invitation"
    MEMORY = "Memory"
    COFFIN = "Coffin"
    ALLFLAME_EMBER = "AllflameEmber"
    MAP = "Map"
    UNIQUE_MAP = "UniqueMap"
    HELMET_ENCHANT = "HelmetEnchant"

@dataclass
class FetchMetrics:
    """Metrics for API fetch operations"""
    endpoint: str
    league: str
    status_code: int
    latency_ms: float
    etag: Optional[str]
    rows_ingested: int
    timestamp: datetime
    error: Optional[str] = None

@dataclass
class RateLimitState:
    """Rate limiting state"""
    requests_per_minute: int = 0
    last_reset: datetime = datetime.now()
    global_requests: int = 0
    endpoint_requests: Dict[str, int] = None
    
    def __post_init__(self):
        if self.endpoint_requests is None:
            self.endpoint_requests = {}

class PoeNinjaAPI:
    """
    Production-ready PoE.ninja API client with comprehensive error handling,
    rate limiting, and monitoring capabilities
    """
    
    def __init__(self, league: str = "Mercenaries", max_concurrent: int = 6):
        self.base_url = "https://poe.ninja/api/data"
        self.league = league
        self.max_concurrent = max_concurrent
        self.rate_limit = RateLimitState()
        self.session: Optional[aiohttp.ClientSession] = None
        self.metrics: List[FetchMetrics] = []
        
        # Rate limiting thresholds from blueprint
        self.max_requests_per_minute = 45
        self.max_endpoint_requests_per_minute = 5
        
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={
                'User-Agent': 'PoE-Dashboard/1.0 (Respectful API Client)',
                'Accept': 'application/json',
                'Accept-Encoding': 'gzip, deflate'
            }
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
            
    def _check_rate_limit(self, endpoint: str) -> bool:
        """Check if request is within rate limits"""
        now = datetime.now()
        
        # Reset counters every minute
        if (now - self.rate_limit.last_reset).total_seconds() >= 60:
            self.rate_limit.requests_per_minute = 0
            self.rate_limit.endpoint_requests.clear()
            self.rate_limit.last_reset = now
            
        # Check global rate limit (45 req/min)
        if self.rate_limit.requests_per_minute >= self.max_requests_per_minute:
            logger.warning("Global rate limit exceeded")
            return False
            
        # Check endpoint-specific rate limit (5 req/min per endpoint)
        endpoint_count = self.rate_limit.endpoint_requests.get(endpoint, 0)
        if endpoint_count >= self.max_endpoint_requests_per_minute:
            logger.warning(f"Endpoint rate limit exceeded for {endpoint}")
            return False
            
        return True
        
    def _update_rate_limit(self, endpoint: str):
        """Update rate limit counters"""
        self.rate_limit.requests_per_minute += 1
        self.rate_limit.endpoint_requests[endpoint] = \
            self.rate_limit.endpoint_requests.get(endpoint, 0) + 1
            
    async def _fetch_with_retry(self, url: str, endpoint: str,
                               max_retries: int = 3) -> Tuple[Optional[Dict], FetchMetrics]:
        """
        Fetch data with exponential backoff retry logic
        Returns (data, metrics)
        """
        start_time = time.time()
        logger.debug(f"Starting fetch for {endpoint} from {url}")

        for attempt in range(max_retries + 1):
            try:
                # Check rate limits
                if not self._check_rate_limit(endpoint):
                    logger.debug(f"Rate limit exceeded for {endpoint}, waiting 60s")
                    await asyncio.sleep(60)  # Wait for rate limit reset
                    continue

                # Add jitter to avoid thundering herd
                if attempt > 0:
                    jitter = random.uniform(0, 0.5 * (2 ** attempt))
                    logger.debug(f"Retry attempt {attempt + 1} for {endpoint}, jitter: {jitter:.2f}s")
                    await asyncio.sleep(jitter)

                self._update_rate_limit(endpoint)
                logger.debug(f"Rate limit state - Total: {self.rate_limit.requests_per_minute}/45, "
                           f"Endpoint {endpoint}: {self.rate_limit.endpoint_requests.get(endpoint, 0)}/5")

                async with self.session.get(url) as response:
                    latency_ms = (time.time() - start_time) * 1000
                    etag = response.headers.get('ETag')
                    content_length = response.headers.get('Content-Length', 'unknown')

                    logger.debug(f"HTTP {response.status} for {endpoint} - "
                               f"Latency: {latency_ms:.1f}ms, "
                               f"Content-Length: {content_length}, "
                               f"ETag: {etag}")

                    if response.status == 200:
                        data = await response.json()
                        rows_count = len(data.get('lines', []))

                        # Debug log response structure
                        logger.debug(f"Response structure for {endpoint}: "
                                   f"lines={rows_count}, "
                                   f"currencyDetails={len(data.get('currencyDetails', []))}, "
                                   f"keys={list(data.keys())}")

                        metrics = FetchMetrics(
                            endpoint=endpoint,
                            league=self.league,
                            status_code=response.status,
                            latency_ms=latency_ms,
                            etag=etag,
                            rows_ingested=rows_count,
                            timestamp=datetime.now()
                        )

                        self.metrics.append(metrics)
                        logger.info(f"Successfully fetched {endpoint}: {rows_count} rows, {latency_ms:.1f}ms")
                        return data, metrics

                    elif response.status == 429:  # Rate limited
                        retry_after = response.headers.get('Retry-After', '60')
                        logger.warning(f"Rate limited on {endpoint}, attempt {attempt + 1}, "
                                     f"Retry-After: {retry_after}s")
                        await asyncio.sleep(2 ** attempt)
                        continue
                        
                    else:
                        error_msg = f"HTTP {response.status} for {endpoint}"
                        response_text = await response.text()
                        logger.error(error_msg)
                        logger.debug(f"Error response body for {endpoint}: {response_text[:200]}...")

                        metrics = FetchMetrics(
                            endpoint=endpoint,
                            league=self.league,
                            status_code=response.status,
                            latency_ms=latency_ms,
                            etag=etag,
                            rows_ingested=0,
                            timestamp=datetime.now(),
                            error=error_msg
                        )

                        self.metrics.append(metrics)

                        if attempt == max_retries:
                            return None, metrics

                        await asyncio.sleep(2 ** attempt)

            except asyncio.TimeoutError as e:
                error_msg = f"Timeout fetching {endpoint}: {str(e)}"
                logger.error(error_msg)
                logger.debug(f"Timeout after {time.time() - start_time:.1f}s for {url}")

                if attempt == max_retries:
                    metrics = FetchMetrics(
                        endpoint=endpoint,
                        league=self.league,
                        status_code=0,
                        latency_ms=(time.time() - start_time) * 1000,
                        etag=None,
                        rows_ingested=0,
                        timestamp=datetime.now(),
                        error=error_msg
                    )
                    self.metrics.append(metrics)
                    return None, metrics

                await asyncio.sleep(2 ** attempt)

            except Exception as e:
                error_msg = f"Exception fetching {endpoint}: {str(e)}"
                logger.error(error_msg)
                logger.debug(f"Exception details for {endpoint}: {type(e).__name__}: {str(e)}")

                if attempt == max_retries:
                    metrics = FetchMetrics(
                        endpoint=endpoint,
                        league=self.league,
                        status_code=0,
                        latency_ms=(time.time() - start_time) * 1000,
                        etag=None,
                        rows_ingested=0,
                        timestamp=datetime.now(),
                        error=error_msg
                    )
                    self.metrics.append(metrics)
                    return None, metrics

                await asyncio.sleep(2 ** attempt)
                
        return None, None

    async def get_league_state(self) -> Optional[Dict]:
        """Get current league information - simplified version"""
        # Note: The getindexstate endpoint may not be publicly available
        # For now, return a mock league state with common leagues
        return {
            "economyLeagues": [
                {"name": "Standard", "displayName": "Standard", "hardcore": False, "indexed": True},
                {"name": "Hardcore", "displayName": "Hardcore", "hardcore": True, "indexed": True},
                {"name": "Mercenaries", "displayName": "Mercenaries", "hardcore": False, "indexed": True}
            ]
        }
        
    async def get_currency_data(self) -> Optional[Dict]:
        """Get currency exchange rates"""
        url = f"{self.base_url}/currencyoverview?league={self.league}&type=Currency"
        data, metrics = await self._fetch_with_retry(url, "Currency")
        return data

    async def get_fragments_data(self) -> Optional[Dict]:
        """Get fragment prices"""
        url = f"{self.base_url}/currencyoverview?league={self.league}&type=Fragment"
        data, metrics = await self._fetch_with_retry(url, "Fragment")
        return data

    async def get_items_data(self, item_type: EndpointType) -> Optional[Dict]:
        """Get item prices for specified type"""
        url = f"{self.base_url}/itemoverview?league={self.league}&type={item_type.value}"
        data, metrics = await self._fetch_with_retry(url, item_type.value)
        return data
        
    async def fetch_all_endpoints(self) -> Dict[str, Optional[Dict]]:
        """
        Fetch all endpoints in parallel with concurrency control
        Returns dict mapping endpoint names to their data
        """
        logger.info(f"Starting parallel fetch for league {self.league}")
        logger.debug(f"Max concurrent requests: {self.max_concurrent}")

        # Define all endpoints to fetch
        currency_endpoints = [EndpointType.CURRENCY, EndpointType.FRAGMENT]
        item_endpoints = [e for e in EndpointType if e not in currency_endpoints]

        logger.debug(f"Currency endpoints: {[e.value for e in currency_endpoints]}")
        logger.debug(f"Item endpoints: {[e.value for e in item_endpoints]}")

        # Create semaphore for concurrency control
        semaphore = asyncio.Semaphore(self.max_concurrent)

        async def fetch_endpoint(endpoint_type: EndpointType):
            logger.debug(f"Starting fetch for endpoint: {endpoint_type.value}")
            start_time = time.time()

            async with semaphore:
                try:
                    if endpoint_type in currency_endpoints:
                        if endpoint_type == EndpointType.CURRENCY:
                            result = endpoint_type.value, await self.get_currency_data()
                        else:
                            result = endpoint_type.value, await self.get_fragments_data()
                    else:
                        result = endpoint_type.value, await self.get_items_data(endpoint_type)

                    duration = time.time() - start_time
                    logger.debug(f"Completed fetch for {endpoint_type.value} in {duration:.2f}s")
                    return result

                except Exception as e:
                    duration = time.time() - start_time
                    logger.error(f"Failed to fetch {endpoint_type.value} after {duration:.2f}s: {e}")
                    raise

        # Execute all fetches in parallel
        logger.debug(f"Starting {len(EndpointType)} parallel fetch tasks")
        tasks = [fetch_endpoint(endpoint) for endpoint in EndpointType]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Process results
        data_dict = {}
        successful_fetches = 0
        failed_fetches = 0

        for result in results:
            if isinstance(result, Exception):
                logger.error(f"Task failed: {result}")
                failed_fetches += 1
                continue
            endpoint_name, data = result
            data_dict[endpoint_name] = data
            successful_fetches += 1

        logger.info(f"Completed parallel fetch: {successful_fetches} successful, {failed_fetches} failed")
        logger.debug(f"Successful endpoints: {list(data_dict.keys())}")

        return data_dict
        
    def get_metrics_summary(self) -> Dict:
        """Get summary of fetch metrics for monitoring"""
        if not self.metrics:
            return {}
            
        recent_metrics = [m for m in self.metrics 
                         if (datetime.now() - m.timestamp).total_seconds() < 3600]
        
        if not recent_metrics:
            return {}
            
        success_rate = len([m for m in recent_metrics if m.status_code == 200]) / len(recent_metrics)
        avg_latency = sum(m.latency_ms for m in recent_metrics) / len(recent_metrics)
        total_rows = sum(m.rows_ingested for m in recent_metrics)
        
        return {
            'success_rate': success_rate,
            'avg_latency_ms': avg_latency,
            'total_requests': len(recent_metrics),
            'total_rows_ingested': total_rows,
            'last_update': max(m.timestamp for m in recent_metrics).isoformat()
        }
