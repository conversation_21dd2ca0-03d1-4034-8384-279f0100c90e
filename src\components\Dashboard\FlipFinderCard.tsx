import React from 'react';
import { TrendingUp, ExternalLink } from 'lucide-react';
import type { FlipOpportunity } from '../../types';
import { formatCurrency, formatPercentage, getRiskLevelColor } from '../../utils/tauri';

interface FlipFinderCardProps {
  opportunities: FlipOpportunity[];
  onViewAll: () => void;
}

const FlipFinderCard: React.FC<FlipFinderCardProps> = ({ opportunities, onViewAll }) => {
  return (
    <div className="card p-6" data-testid="flip-finder-card">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold flex items-center text-gray-900 dark:text-gray-100">
          <TrendingUp className="w-5 h-5 mr-2" />
          Top Flip Opportunities
        </h2>
        <button
          onClick={onViewAll}
          className="text-poe-accent hover:text-blue-600 text-sm font-medium flex items-center"
        >
          View All
          <ExternalLink className="w-4 h-4 ml-1" />
        </button>
      </div>

      {opportunities.length === 0 ? (
        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
          <TrendingUp className="w-12 h-12 mx-auto mb-3 opacity-50" />
          <p>No flip opportunities found</p>
          <p className="text-sm">Try adjusting your search criteria</p>
        </div>
      ) : (
        <div className="space-y-3" data-testid="flip-opportunities">
          {opportunities.slice(0, 5).map((opportunity, index) => (
            <div
              key={index}
              className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
              data-testid="flip-opportunity"
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900 dark:text-gray-100" data-testid="item-name">
                    {opportunity.item_name}
                  </h3>
                  <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                    <span data-testid="buy-price">
                      Buy: {formatCurrency(opportunity.buy_price, 'chaos')}
                    </span>
                    <span>
                      Sell: {formatCurrency(opportunity.sell_price, 'chaos')}
                    </span>
                    <span className="text-xs">
                      {opportunity.listing_count} listings
                    </span>
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="font-semibold profit-positive" data-testid="profit-pct">
                    {formatPercentage(opportunity.profit_pct)}
                  </div>
                  <div className="text-sm profit-positive">
                    +{formatCurrency(opportunity.profit_chaos, 'chaos')}
                  </div>
                  <div className={`text-xs px-2 py-1 rounded ${getRiskLevelColor(opportunity.risk_level)}`}>
                    {opportunity.risk_level} Risk
                  </div>
                </div>
              </div>
              
              <div className="mt-2 flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                <span>
                  Confidence: {(opportunity.confidence * 100).toFixed(0)}%
                </span>
                <span>
                  Est. sell time: {opportunity.time_to_sell_estimate}
                </span>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default FlipFinderCard;
