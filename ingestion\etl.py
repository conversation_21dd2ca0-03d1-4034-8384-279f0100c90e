"""
ETL Pipeline Component
=====================
Transforms raw JSON data into typed rows with calculated metrics
Implements the blueprint's ETL requirements with materialized view updates
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
import json
import statistics
from dataclasses import dataclass

from ingestion.validator import ValidationReport, ValidationResult
from database.connection import DatabaseConnection

logger = logging.getLogger(__name__)

@dataclass
class ETLResult:
    """Result of ETL processing"""
    endpoint: str
    success: bool
    rows_processed: int
    rows_inserted: int
    error_message: Optional[str] = None

class ETLPipeline:
    """
    ETL Pipeline that:
    - Explodes JSON arrays into typed rows in prices table
    - Calculates volatility (σ), momentum (Δ7d), liquidity score
    - Updates materialized views for current prices, top movers, etc.
    - Handles league discovery and item catalog updates
    """
    
    def __init__(self, database_url: str):
        self.database_url = database_url
        self.db_connection: Optional[DatabaseConnection] = None
        
    async def initialize(self):
        """Initialize database connection"""
        self.db_connection = DatabaseConnection(self.database_url)
        await self.db_connection.connect()
        
    async def process_batch(self, 
                          raw_data: Dict[str, Optional[Dict[str, Any]]],
                          validation_results: Dict[str, ValidationReport]) -> Dict[str, ETLResult]:
        """
        Process a batch of raw data through the ETL pipeline
        
        Args:
            raw_data: Raw JSON data from API
            validation_results: Validation reports for each endpoint
            
        Returns:
            Dict mapping endpoint names to ETL results
        """
        logger.info(f"Starting ETL processing for {len(raw_data)} endpoints")
        
        etl_results = {}
        
        # Process league state first
        if 'league_state' in raw_data and raw_data['league_state']:
            await self._process_league_state(raw_data['league_state'])
            
        # Process each endpoint
        for endpoint, data in raw_data.items():
            if endpoint == 'league_state':
                continue  # Already processed
                
            validation_report = validation_results.get(endpoint)
            
            if not validation_report or validation_report.result in [ValidationResult.INVALID, ValidationResult.CORRUPT]:
                etl_results[endpoint] = ETLResult(
                    endpoint=endpoint,
                    success=False,
                    rows_processed=0,
                    rows_inserted=0,
                    error_message=f"Skipped due to validation: {validation_report.result.value if validation_report else 'No validation report'}"
                )
                continue
                
            if not data:
                etl_results[endpoint] = ETLResult(
                    endpoint=endpoint,
                    success=False,
                    rows_processed=0,
                    rows_inserted=0,
                    error_message="No data to process"
                )
                continue
                
            try:
                result = await self._process_endpoint_data(endpoint, data, validation_report)
                etl_results[endpoint] = result
                
            except Exception as e:
                logger.error(f"ETL processing failed for {endpoint}: {e}")
                etl_results[endpoint] = ETLResult(
                    endpoint=endpoint,
                    success=False,
                    rows_processed=0,
                    rows_inserted=0,
                    error_message=str(e)
                )
                
        # Refresh materialized views
        await self._refresh_materialized_views()
        
        # Log summary
        successful_etl = sum(1 for r in etl_results.values() if r.success)
        total_rows_inserted = sum(r.rows_inserted for r in etl_results.values())
        
        logger.info(f"ETL processing complete: {successful_etl}/{len(etl_results)} endpoints successful, {total_rows_inserted} total rows inserted")
        
        return etl_results
        
    async def _process_league_state(self, league_data: Dict[str, Any]):
        """Process league state data to update leagues table"""
        try:
            economy_leagues = league_data.get('economyLeagues', [])
            
            for league_info in economy_leagues:
                league_name = league_info.get('name')
                display_name = league_info.get('displayName', league_name)
                
                if not league_name:
                    continue
                    
                # Upsert league
                query = """
                    INSERT INTO leagues (name, display_name, is_active, last_seen)
                    VALUES ($1, $2, true, CURRENT_TIMESTAMP)
                    ON CONFLICT (name) 
                    DO UPDATE SET 
                        display_name = EXCLUDED.display_name,
                        is_active = true,
                        last_seen = CURRENT_TIMESTAMP,
                        updated_at = CURRENT_TIMESTAMP
                """
                
                await self.db_connection.execute(query, league_name, display_name)
                
            logger.info(f"Updated {len(economy_leagues)} leagues")
            
        except Exception as e:
            logger.error(f"Failed to process league state: {e}")
            
    async def _process_endpoint_data(self, 
                                   endpoint: str, 
                                   data: Dict[str, Any],
                                   validation_report: ValidationReport) -> ETLResult:
        """Process data for a specific endpoint"""
        
        lines = data.get('lines', [])
        if not lines:
            return ETLResult(
                endpoint=endpoint,
                success=True,
                rows_processed=0,
                rows_inserted=0
            )
            
        # Determine processing method based on endpoint type
        if endpoint in ['Currency', 'Fragment']:
            return await self._process_currency_data(endpoint, lines, validation_report)
        else:
            return await self._process_item_data(endpoint, lines, validation_report)
            
    async def _process_currency_data(self, 
                                   endpoint: str,
                                   lines: List[Dict[str, Any]],
                                   validation_report: ValidationReport) -> ETLResult:
        """Process currency/fragment data"""
        
        rows_processed = len(lines)
        rows_inserted = 0
        
        # Get league ID
        league_id = await self._get_league_id("Mercenaries")  # TODO: Make configurable
        if not league_id:
            raise Exception("League not found")
            
        for line in lines:
            try:
                # Extract currency info
                currency_name = line.get('currencyTypeName')
                if not currency_name:
                    continue
                    
                # Create or get item
                item_id = await self._upsert_currency_item(currency_name, endpoint)
                
                # Extract pricing data
                chaos_equivalent = line.get('chaosEquivalent', 0)
                pay_value = line.get('pay', {}).get('value', 0) if line.get('pay') else None
                receive_value = line.get('receive', {}).get('value', 0) if line.get('receive') else None
                listing_count = line.get('listingCount', 0)
                count = line.get('count', 0)
                
                # Extract sparkline data
                sparkline_data = None
                sparkline_low = None
                sparkline_high = None
                
                pay_sparkline = line.get('paySparkLine', {})
                if pay_sparkline and 'data' in pay_sparkline:
                    sparkline_data = pay_sparkline['data']
                    if sparkline_data:
                        sparkline_low = min(sparkline_data)
                        sparkline_high = max(sparkline_data)
                        
                # Calculate metrics
                volatility = self._calculate_volatility(sparkline_data) if sparkline_data else None
                momentum_7d = pay_sparkline.get('totalChange', 0) if pay_sparkline else 0
                liquidity_score = self._calculate_liquidity_score(listing_count, endpoint)
                confidence_score = self._calculate_confidence_score(count, listing_count, validation_report.confidence_score)
                
                # Insert price record
                await self._insert_price_record(
                    item_id=item_id,
                    league_id=league_id,
                    chaos_value=chaos_equivalent,
                    divine_value=None,  # Not available for currency
                    pay_value=pay_value,
                    receive_value=receive_value,
                    listing_count=listing_count,
                    sparkline_data=sparkline_data,
                    sparkline_low=sparkline_low,
                    sparkline_high=sparkline_high,
                    volatility=volatility,
                    momentum_7d=momentum_7d,
                    liquidity_score=liquidity_score,
                    confidence_score=confidence_score
                )
                
                rows_inserted += 1
                
            except Exception as e:
                logger.warning(f"Failed to process currency line {currency_name}: {e}")
                continue
                
        return ETLResult(
            endpoint=endpoint,
            success=True,
            rows_processed=rows_processed,
            rows_inserted=rows_inserted
        )
        
    async def _process_item_data(self,
                               endpoint: str,
                               lines: List[Dict[str, Any]],
                               validation_report: ValidationReport) -> ETLResult:
        """Process item data"""
        
        rows_processed = len(lines)
        rows_inserted = 0
        
        # Get league ID
        league_id = await self._get_league_id("Mercenaries")  # TODO: Make configurable
        if not league_id:
            raise Exception("League not found")
            
        for line in lines:
            try:
                # Extract item info
                item_name = line.get('name')
                if not item_name:
                    continue
                    
                details_id = line.get('detailsId', f"{endpoint}_{item_name}")
                
                # Create or get item
                item_id = await self._upsert_item(
                    details_id=details_id,
                    name=item_name,
                    item_type=endpoint,
                    base_type=line.get('baseType'),
                    icon_url=line.get('icon')
                )
                
                # Extract pricing data
                chaos_value = line.get('chaosValue', 0)
                divine_value = line.get('divineValue', 0)
                listing_count = line.get('listingCount', 0)
                count = line.get('count', 0)
                
                # Extract sparkline data
                sparkline_data = None
                sparkline_low = None
                sparkline_high = None
                momentum_7d = 0
                
                sparkline = line.get('sparkline', {})
                if sparkline and 'data' in sparkline:
                    sparkline_data = sparkline['data']
                    if sparkline_data:
                        sparkline_low = min(sparkline_data)
                        sparkline_high = max(sparkline_data)
                    momentum_7d = sparkline.get('totalChange', 0)
                    
                # Calculate metrics
                volatility = self._calculate_volatility(sparkline_data) if sparkline_data else None
                liquidity_score = self._calculate_liquidity_score(listing_count, endpoint)
                confidence_score = self._calculate_confidence_score(count, listing_count, validation_report.confidence_score)
                
                # Insert price record
                await self._insert_price_record(
                    item_id=item_id,
                    league_id=league_id,
                    chaos_value=chaos_value if chaos_value > 0 else None,
                    divine_value=divine_value if divine_value > 0 else None,
                    pay_value=None,
                    receive_value=None,
                    listing_count=listing_count,
                    sparkline_data=sparkline_data,
                    sparkline_low=sparkline_low,
                    sparkline_high=sparkline_high,
                    volatility=volatility,
                    momentum_7d=momentum_7d,
                    liquidity_score=liquidity_score,
                    confidence_score=confidence_score
                )
                
                rows_inserted += 1
                
            except Exception as e:
                logger.warning(f"Failed to process item line {item_name}: {e}")
                continue
                
        return ETLResult(
            endpoint=endpoint,
            success=True,
            rows_processed=rows_processed,
            rows_inserted=rows_inserted
        )
        
    async def _get_league_id(self, league_name: str) -> Optional[int]:
        """Get league ID by name"""
        query = "SELECT id FROM leagues WHERE name = $1"
        result = await self.db_connection.fetch_one(query, league_name)
        return result['id'] if result else None
        
    async def _upsert_currency_item(self, currency_name: str, endpoint: str) -> int:
        """Create or update currency item"""
        details_id = f"{endpoint}_{currency_name}"
        
        query = """
            INSERT INTO items (details_id, name, type, category)
            VALUES ($1, $2, $3, 'Currency')
            ON CONFLICT (details_id, type)
            DO UPDATE SET 
                name = EXCLUDED.name,
                updated_at = CURRENT_TIMESTAMP
            RETURNING id
        """
        
        result = await self.db_connection.fetch_one(query, details_id, currency_name, endpoint)
        return result['id']
        
    async def _upsert_item(self, details_id: str, name: str, item_type: str, 
                          base_type: Optional[str], icon_url: Optional[str]) -> int:
        """Create or update item"""
        query = """
            INSERT INTO items (details_id, name, type, base_type, icon_url)
            VALUES ($1, $2, $3, $4, $5)
            ON CONFLICT (details_id, type)
            DO UPDATE SET 
                name = EXCLUDED.name,
                base_type = EXCLUDED.base_type,
                icon_url = EXCLUDED.icon_url,
                updated_at = CURRENT_TIMESTAMP
            RETURNING id
        """
        
        result = await self.db_connection.fetch_one(query, details_id, name, item_type, base_type, icon_url)
        return result['id']
        
    async def _insert_price_record(self, **kwargs):
        """Insert a price record"""
        query = """
            INSERT INTO prices (
                item_id, league_id, chaos_value, divine_value, pay_value, receive_value,
                listing_count, sparkline_data, sparkline_low, sparkline_high,
                volatility, momentum_7d, liquidity_score, confidence_score,
                is_outlier, snapshot_time
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16
            )
        """
        
        is_outlier = kwargs.get('volatility', 0) > 4.0 if kwargs.get('volatility') else False
        
        await self.db_connection.execute(
            query,
            kwargs['item_id'],
            kwargs['league_id'],
            kwargs['chaos_value'],
            kwargs['divine_value'],
            kwargs['pay_value'],
            kwargs['receive_value'],
            kwargs['listing_count'],
            kwargs['sparkline_data'],
            kwargs['sparkline_low'],
            kwargs['sparkline_high'],
            kwargs['volatility'],
            kwargs['momentum_7d'],
            kwargs['liquidity_score'],
            kwargs['confidence_score'],
            is_outlier,
            datetime.now()
        )
        
    def _calculate_volatility(self, sparkline_data: List[float]) -> Optional[float]:
        """Calculate price volatility (standard deviation)"""
        if not sparkline_data or len(sparkline_data) < 2:
            return None
            
        try:
            return statistics.stdev(sparkline_data)
        except statistics.StatisticsError:
            return None
            
    def _calculate_liquidity_score(self, listing_count: int, endpoint: str) -> float:
        """Calculate normalized liquidity score"""
        # Rough max listing counts by endpoint type (can be refined)
        max_listings = {
            'Currency': 1000,
            'Fragment': 500,
            'DivinationCard': 200,
            'UniqueWeapon': 100,
            'Map': 300
        }
        
        max_for_type = max_listings.get(endpoint, 100)
        return min(1.0, listing_count / max_for_type)
        
    def _calculate_confidence_score(self, count: int, listing_count: int, validation_confidence: float) -> float:
        """Calculate overall confidence score"""
        # Base confidence from validation
        confidence = validation_confidence
        
        # Reduce confidence for low counts
        if count < 5:
            confidence *= 0.7
        if listing_count < 10:
            confidence *= 0.8
            
        return max(0.1, confidence)
        
    async def _refresh_materialized_views(self):
        """Refresh all materialized views"""
        try:
            await self.db_connection.execute("SELECT refresh_price_views()")
            logger.info("Materialized views refreshed successfully")
        except Exception as e:
            logger.error(f"Failed to refresh materialized views: {e}")
            
    async def cleanup(self):
        """Cleanup resources"""
        if self.db_connection:
            await self.db_connection.close()
