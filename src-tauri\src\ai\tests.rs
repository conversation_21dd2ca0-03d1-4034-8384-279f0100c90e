#[cfg(test)]
mod tests {
    use super::*;
    use crate::data::DatabaseManager;
    use crate::models::*;
    use chrono::Utc;
    use tempfile::tempdir;

    async fn create_test_db() -> DatabaseManager {
        let temp_dir = tempdir().unwrap();
        let db_path = temp_dir.path().join("test.db");
        DatabaseManager::new(db_path.to_str().unwrap()).await.unwrap()
    }

    async fn setup_test_data(db: &DatabaseManager) -> (i64, i64) {
        // Create test league
        let league_id = db.upsert_league("TestLeague", "Test League").await.unwrap();
        
        // Create test item
        let test_item = PoeNinjaItem {
            currency_type_name: Some("Test Item".to_string()),
            chaos_equivalent: Some(100.0),
            divine_equivalent: Some(0.5),
            listing_count: Some(50),
            name: Some("Test Item".to_string()),
            type_line: Some("Test Type".to_string()),
            base_type: Some("Test Base".to_string()),
            icon: Some("test_icon.png".to_string()),
            details_id: Some("test_item_1".to_string()),
        };
        
        let item_id = db.upsert_item(&test_item, league_id, "Currency").await.unwrap();
        
        // Insert some historical price data
        for i in 0..10 {
            let price_variation = 100.0 + (i as f64 * 2.0); // Gradual price increase
            let test_item_variant = PoeNinjaItem {
                chaos_equivalent: Some(price_variation),
                divine_equivalent: Some(price_variation / 200.0),
                listing_count: Some(45 + i),
                ..test_item.clone()
            };
            
            db.insert_price(item_id, league_id, &test_item_variant).await.unwrap();
            
            // Add some delay to create time series
            tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
        }
        
        (item_id, league_id)
    }

    #[tokio::test]
    async fn test_price_predictor_initialization() {
        let db = create_test_db().await;
        let (item_id, league_id) = setup_test_data(&db).await;
        
        let mut predictor = PricePredictor::new();
        
        // Training should complete without errors
        let result = predictor.train_models(&db).await;
        assert!(result.is_ok(), "Model training failed: {:?}", result);
        
        // Should have at least one model trained
        assert!(!predictor.models.is_empty(), "No models were trained");
    }

    #[tokio::test]
    async fn test_price_prediction() {
        let db = create_test_db().await;
        let (item_id, league_id) = setup_test_data(&db).await;
        
        let mut predictor = PricePredictor::new();
        predictor.train_models(&db).await.unwrap();
        
        // Make a prediction
        let prediction = predictor.predict_price(&db, item_id, "Currency", 24).await;
        
        assert!(prediction.is_ok(), "Price prediction failed: {:?}", prediction);
        
        let pred = prediction.unwrap();
        assert!(pred.predicted_price > 0.0, "Predicted price should be positive");
        assert!(pred.confidence > 0.0 && pred.confidence <= 1.0, "Confidence should be between 0 and 1");
        assert_eq!(pred.prediction_horizon_hours, 24);
    }

    #[tokio::test]
    async fn test_market_analyzer() {
        let db = create_test_db().await;
        let (item_id, league_id) = setup_test_data(&db).await;
        
        let mut analyzer = MarketAnalyzer::new();
        analyzer.initialize(&db).await.unwrap();
        
        // Test flip opportunity analysis
        let opportunities = analyzer.analyze_flip_opportunities(
            &db,
            1000.0,  // budget
            5.0,     // min profit %
            RiskLevel::High,
            "TestLeague"
        ).await;
        
        assert!(opportunities.is_ok(), "Flip analysis failed: {:?}", opportunities);
        
        let opps = opportunities.unwrap();
        // Should find at least one opportunity with our test data
        assert!(!opps.is_empty(), "No flip opportunities found");
        
        for opp in &opps {
            assert!(opp.confidence_score >= 0.0 && opp.confidence_score <= 1.0);
            assert!(opp.risk_score >= 0.0 && opp.risk_score <= 1.0);
            assert!(opp.liquidity_score >= 0.0 && opp.liquidity_score <= 1.0);
        }
    }

    #[tokio::test]
    async fn test_data_quality_report() {
        let db = create_test_db().await;
        let (item_id, league_id) = setup_test_data(&db).await;
        
        let mut analyzer = MarketAnalyzer::new();
        analyzer.initialize(&db).await.unwrap();
        
        let report = analyzer.generate_data_quality_report(&db).await;
        
        assert!(report.is_ok(), "Quality report generation failed: {:?}", report);
        
        let quality_report = report.unwrap();
        assert!(quality_report.total_items_checked > 0);
        assert!(quality_report.overall_score >= 0.0 && quality_report.overall_score <= 1.0);
        assert!(quality_report.data_freshness_score >= 0.0 && quality_report.data_freshness_score <= 1.0);
    }

    #[tokio::test]
    async fn test_data_validator() {
        use crate::data::validator::DataValidator;
        
        let validator = DataValidator::new();
        
        // Create test price data
        let good_price = Price {
            id: 1,
            item_id: 1,
            league_id: 1,
            chaos_value: Some(100.0),
            divine_value: Some(0.5),
            listing_count: 50,
            volatility: None,
            momentum_7d: None,
            liquidity_score: None,
            confidence_score: 0.9,
            is_outlier: false,
            snapshot_time: Utc::now(),
        };
        
        let bad_price = Price {
            id: 2,
            item_id: 1,
            league_id: 1,
            chaos_value: None, // Missing price data
            divine_value: None,
            listing_count: -5, // Invalid listing count
            volatility: None,
            momentum_7d: None,
            liquidity_score: None,
            confidence_score: 0.05, // Very low confidence
            is_outlier: false,
            snapshot_time: Utc::now() - chrono::Duration::hours(10), // Stale data
        };
        
        let historical_prices = vec![
            Price {
                chaos_value: Some(95.0),
                snapshot_time: Utc::now() - chrono::Duration::hours(1),
                ..good_price.clone()
            },
            Price {
                chaos_value: Some(98.0),
                snapshot_time: Utc::now() - chrono::Duration::hours(2),
                ..good_price.clone()
            },
        ];
        
        // Test good price validation
        let good_result = validator.validate_price(&good_price, &historical_prices);
        assert!(good_result.is_valid, "Good price should be valid");
        assert!(good_result.issues.is_empty(), "Good price should have no issues");
        
        // Test bad price validation
        let bad_result = validator.validate_price(&bad_price, &historical_prices);
        assert!(!bad_result.is_valid, "Bad price should be invalid");
        assert!(!bad_result.issues.is_empty(), "Bad price should have issues");
        assert!(bad_result.confidence_adjustment < 0.0, "Bad price should have confidence penalty");
    }

    #[tokio::test]
    async fn test_feature_extraction() {
        let db = create_test_db().await;
        let (item_id, league_id) = setup_test_data(&db).await;
        
        let predictor = PricePredictor::new();
        
        // Get price history
        let prices = db.get_price_history(item_id, 7).await.unwrap();
        assert!(!prices.is_empty(), "Should have price history");
        
        // Extract features
        let features = predictor.extract_market_features(&prices, item_id).await;
        assert!(features.is_ok(), "Feature extraction failed: {:?}", features);
        
        let market_features = features.unwrap();
        assert!(market_features.current_price > 0.0);
        assert!(market_features.price_7d_avg > 0.0);
        assert!(market_features.listing_count > 0);
    }

    #[tokio::test]
    async fn test_risk_calculation() {
        let db = create_test_db().await;
        let (item_id, league_id) = setup_test_data(&db).await;
        
        let analyzer = MarketAnalyzer::new();
        
        // Get test item and price
        let item = Item {
            id: item_id,
            details_id: "test_item".to_string(),
            name: "Test Item".to_string(),
            type_line: Some("Test Type".to_string()),
            base_type: Some("Test Base".to_string()),
            category: "Currency".to_string(),
            subcategory: None,
            icon_url: None,
            league_id,
        };
        
        let price = db.get_latest_price(item_id, "TestLeague").await.unwrap();
        
        let risk_score = analyzer.calculate_risk_score(&db, &item, &price).await;
        assert!(risk_score.is_ok(), "Risk calculation failed: {:?}", risk_score);
        
        let risk = risk_score.unwrap();
        assert!(risk >= 0.0 && risk <= 1.0, "Risk score should be between 0 and 1");
    }
}
