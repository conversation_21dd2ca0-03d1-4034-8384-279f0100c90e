{"$message_type":"diagnostic","message":"unused import: `State`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":226,"byte_end":231,"line_start":7,"line_end":7,"column_start":13,"column_end":18,"is_primary":true,"text":[{"text":"use tauri::{State, Manager};","highlight_start":13,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":226,"byte_end":233,"line_start":7,"line_end":7,"column_start":13,"column_end":20,"is_primary":true,"text":[{"text":"use tauri::{State, Manager};","highlight_start":13,"highlight_end":20}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":225,"byte_end":226,"line_start":7,"line_end":7,"column_start":12,"column_end":13,"is_primary":true,"text":[{"text":"use tauri::{State, Manager};","highlight_start":12,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":240,"byte_end":241,"line_start":7,"line_end":7,"column_start":27,"column_end":28,"is_primary":true,"text":[{"text":"use tauri::{State, Manager};","highlight_start":27,"highlight_end":28}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\main.rs:7:13: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `State`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused imports: `DateTime` and `Utc`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\data\\database.rs","byte_start":83,"byte_end":91,"line_start":4,"line_end":4,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc};","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\data\\database.rs","byte_start":93,"byte_end":96,"line_start":4,"line_end":4,"column_start":24,"column_end":27,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc};","highlight_start":24,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\data\\database.rs","byte_start":70,"byte_end":99,"line_start":4,"line_end":5,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc};","highlight_start":1,"highlight_end":29},{"text":"use fuzzy_matcher::FuzzyMatcher;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\data\\database.rs:4:14: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused imports: `DateTime` and `Utc`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `PoeNinjaItem`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\data\\fetcher.rs","byte_start":282,"byte_end":294,"line_start":8,"line_end":8,"column_start":39,"column_end":51,"is_primary":true,"text":[{"text":"use crate::models::{PoeNinjaResponse, PoeNinjaItem};","highlight_start":39,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\data\\fetcher.rs","byte_start":280,"byte_end":294,"line_start":8,"line_end":8,"column_start":37,"column_end":51,"is_primary":true,"text":[{"text":"use crate::models::{PoeNinjaResponse, PoeNinjaItem};","highlight_start":37,"highlight_end":51}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\data\\fetcher.rs","byte_start":263,"byte_end":264,"line_start":8,"line_end":8,"column_start":20,"column_end":21,"is_primary":true,"text":[{"text":"use crate::models::{PoeNinjaResponse, PoeNinjaItem};","highlight_start":20,"highlight_end":21}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\data\\fetcher.rs","byte_start":294,"byte_end":295,"line_start":8,"line_end":8,"column_start":51,"column_end":52,"is_primary":true,"text":[{"text":"use crate::models::{PoeNinjaResponse, PoeNinjaItem};","highlight_start":51,"highlight_end":52}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\data\\fetcher.rs:8:39: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `PoeNinjaItem`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `Duration`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\data\\validator.rs","byte_start":48,"byte_end":56,"line_start":2,"line_end":2,"column_start":29,"column_end":37,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc, Duration};","highlight_start":29,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\data\\validator.rs","byte_start":46,"byte_end":56,"line_start":2,"line_end":2,"column_start":27,"column_end":37,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc, Duration};","highlight_start":27,"highlight_end":37}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\data\\validator.rs:2:29: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `Duration`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `OrderStatistics`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\data\\validator.rs","byte_start":96,"byte_end":111,"line_start":3,"line_end":3,"column_start":38,"column_end":53,"is_primary":true,"text":[{"text":"use statrs::statistics::{Statistics, OrderStatistics};","highlight_start":38,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\data\\validator.rs","byte_start":94,"byte_end":111,"line_start":3,"line_end":3,"column_start":36,"column_end":53,"is_primary":true,"text":[{"text":"use statrs::statistics::{Statistics, OrderStatistics};","highlight_start":36,"highlight_end":53}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\data\\validator.rs","byte_start":83,"byte_end":84,"line_start":3,"line_end":3,"column_start":25,"column_end":26,"is_primary":true,"text":[{"text":"use statrs::statistics::{Statistics, OrderStatistics};","highlight_start":25,"highlight_end":26}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\data\\validator.rs","byte_start":111,"byte_end":112,"line_start":3,"line_end":3,"column_start":53,"column_end":54,"is_primary":true,"text":[{"text":"use statrs::statistics::{Statistics, OrderStatistics};","highlight_start":53,"highlight_end":54}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\data\\validator.rs:3:38: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `OrderStatistics`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `chrono::Utc`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\price_commands.rs","byte_start":22,"byte_end":33,"line_start":2,"line_end":2,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"use chrono::Utc;","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\commands\\price_commands.rs","byte_start":18,"byte_end":35,"line_start":2,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use chrono::Utc;","highlight_start":1,"highlight_end":17},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\commands\\price_commands.rs:2:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `chrono::Utc`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `models::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":311,"byte_end":320,"line_start":15,"line_end":15,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"use models::*;","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":307,"byte_end":322,"line_start":15,"line_end":16,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use models::*;","highlight_start":1,"highlight_end":15},{"text":"use data::{DatabaseManager, PoeNinjaClient, DataValidator};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\main.rs:15:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `models::*`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `item_name`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\price_commands.rs","byte_start":2471,"byte_end":2480,"line_start":86,"line_end":86,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"    item_name: String,","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\commands\\price_commands.rs","byte_start":2471,"byte_end":2480,"line_start":86,"line_end":86,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"    item_name: String,","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":"_item_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\commands\\price_commands.rs:86:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `item_name`: help: if this is intentional, prefix it with an underscore: `_item_name`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `league`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\price_commands.rs","byte_start":2494,"byte_end":2500,"line_start":87,"line_end":87,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    league: String,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\commands\\price_commands.rs","byte_start":2494,"byte_end":2500,"line_start":87,"line_end":87,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    league: String,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":"_league","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\commands\\price_commands.rs:87:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `league`: help: if this is intentional, prefix it with an underscore: `_league`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `days`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\price_commands.rs","byte_start":2514,"byte_end":2518,"line_start":88,"line_end":88,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    days: i32,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\commands\\price_commands.rs","byte_start":2514,"byte_end":2518,"line_start":88,"line_end":88,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    days: i32,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":"_days","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\commands\\price_commands.rs:88:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `days`: help: if this is intentional, prefix it with an underscore: `_days`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\price_commands.rs","byte_start":2529,"byte_end":2534,"line_start":89,"line_end":89,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    state: State<'_, AppState>,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\commands\\price_commands.rs","byte_start":2529,"byte_end":2534,"line_start":89,"line_end":89,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    state: State<'_, AppState>,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\commands\\price_commands.rs:89:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `state`: help: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `db_manager`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\price_commands.rs","byte_start":2731,"byte_end":2741,"line_start":97,"line_end":97,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    db_manager: &crate::data::DatabaseManager,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\commands\\price_commands.rs","byte_start":2731,"byte_end":2741,"line_start":97,"line_end":97,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    db_manager: &crate::data::DatabaseManager,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":"_db_manager","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\commands\\price_commands.rs:97:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `db_manager`: help: if this is intentional, prefix it with an underscore: `_db_manager`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `item_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\price_commands.rs","byte_start":2778,"byte_end":2785,"line_start":98,"line_end":98,"column_start":5,"column_end":12,"is_primary":true,"text":[{"text":"    item_id: i64,","highlight_start":5,"highlight_end":12}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\commands\\price_commands.rs","byte_start":2778,"byte_end":2785,"line_start":98,"line_end":98,"column_start":5,"column_end":12,"is_primary":true,"text":[{"text":"    item_id: i64,","highlight_start":5,"highlight_end":12}],"label":null,"suggested_replacement":"_item_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\commands\\price_commands.rs:98:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `item_id`: help: if this is intentional, prefix it with an underscore: `_item_id`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `league`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\price_commands.rs","byte_start":2796,"byte_end":2802,"line_start":99,"line_end":99,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    league: &str,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\commands\\price_commands.rs","byte_start":2796,"byte_end":2802,"line_start":99,"line_end":99,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    league: &str,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":"_league","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\commands\\price_commands.rs:99:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `league`: help: if this is intentional, prefix it with an underscore: `_league`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\market_commands.rs","byte_start":1821,"byte_end":1826,"line_start":75,"line_end":75,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    state: State<'_, AppState>,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\commands\\market_commands.rs","byte_start":1821,"byte_end":1826,"line_start":75,"line_end":75,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    state: State<'_, AppState>,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\commands\\market_commands.rs:75:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused variable: `state`: help: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `Statistics`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\data\\validator.rs","byte_start":84,"byte_end":94,"line_start":3,"line_end":3,"column_start":26,"column_end":36,"is_primary":true,"text":[{"text":"use statrs::statistics::{Statistics, OrderStatistics};","highlight_start":26,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0msrc\\data\\validator.rs:3:26: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `Statistics`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"unused import: `Manager`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":233,"byte_end":240,"line_start":7,"line_end":7,"column_start":20,"column_end":27,"is_primary":true,"text":[{"text":"use tauri::{State, Manager};","highlight_start":20,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0msrc\\main.rs:7:20: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: unused import: `Manager`\u001b[0m\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":3511,"byte_end":3524,"line_start":112,"line_end":112,"column_start":24,"column_end":37,"is_primary":true,"text":[{"text":"                    Ok(mut scheduler) => {","highlight_start":24,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_mut)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":3511,"byte_end":3515,"line_start":112,"line_end":112,"column_start":24,"column_end":28,"is_primary":true,"text":[{"text":"                    Ok(mut scheduler) => {","highlight_start":24,"highlight_end":28}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\main.rs:112:24: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: variable does not need to be mutable\u001b[0m\n"}
{"$message_type":"diagnostic","message":"field `max_price_change_pct` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\data\\validator.rs","byte_start":774,"byte_end":787,"line_start":34,"line_end":34,"column_start":12,"column_end":25,"is_primary":false,"text":[{"text":"pub struct DataValidator {","highlight_start":12,"highlight_end":25}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\data\\validator.rs","byte_start":822,"byte_end":842,"line_start":36,"line_end":36,"column_start":5,"column_end":25,"is_primary":true,"text":[{"text":"    max_price_change_pct: f64,","highlight_start":5,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0msrc\\data\\validator.rs:36:5: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: field `max_price_change_pct` is never read\u001b[0m\n"}
{"$message_type":"diagnostic","message":"function `quick_price_lookup` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\price_commands.rs","byte_start":102,"byte_end":120,"line_start":7,"line_end":7,"column_start":14,"column_end":32,"is_primary":true,"text":[{"text":"pub async fn quick_price_lookup(","highlight_start":14,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0msrc\\commands\\price_commands.rs:7:14: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: function `quick_price_lookup` is never used\u001b[0m\n"}
{"$message_type":"diagnostic","message":"function `search_item_names` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\price_commands.rs","byte_start":1864,"byte_end":1881,"line_start":63,"line_end":63,"column_start":14,"column_end":31,"is_primary":true,"text":[{"text":"pub async fn search_item_names(","highlight_start":14,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0msrc\\commands\\price_commands.rs:63:14: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: function `search_item_names` is never used\u001b[0m\n"}
{"$message_type":"diagnostic","message":"function `calculate_trend_7d` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\price_commands.rs","byte_start":2707,"byte_end":2725,"line_start":96,"line_end":96,"column_start":10,"column_end":28,"is_primary":true,"text":[{"text":"async fn calculate_trend_7d(","highlight_start":10,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0msrc\\commands\\price_commands.rs:96:10: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: function `calculate_trend_7d` is never used\u001b[0m\n"}
{"$message_type":"diagnostic","message":"function `classify_liquidity` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\price_commands.rs","byte_start":3002,"byte_end":3020,"line_start":106,"line_end":106,"column_start":4,"column_end":22,"is_primary":true,"text":[{"text":"fn classify_liquidity(listing_count: i32) -> String {","highlight_start":4,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0msrc\\commands\\price_commands.rs:106:4: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: function `classify_liquidity` is never used\u001b[0m\n"}
{"$message_type":"diagnostic","message":"function `set_current_league` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\market_commands.rs","byte_start":1777,"byte_end":1795,"line_start":73,"line_end":73,"column_start":14,"column_end":32,"is_primary":true,"text":[{"text":"pub async fn set_current_league(","highlight_start":14,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0msrc\\commands\\market_commands.rs:73:14: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: function `set_current_league` is never used\u001b[0m\n"}
{"$message_type":"diagnostic","message":"function `get_performance_metrics` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\system_commands.rs","byte_start":756,"byte_end":779,"line_start":29,"line_end":29,"column_start":14,"column_end":37,"is_primary":true,"text":[{"text":"pub async fn get_performance_metrics(","highlight_start":14,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0msrc\\commands\\system_commands.rs:29:14: \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: function `get_performance_metrics` is never used\u001b[0m\n"}
{"$message_type":"diagnostic","message":"25 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m: 25 warnings emitted\u001b[0m\n"}
