#!/usr/bin/env python3
"""
PoE Dashboard Startup Script
===========================
Simple startup script with environment checking and setup assistance
"""

import os
import sys
import subprocess
import asyncio
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 11):
        print("❌ Python 3.11+ is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✓ Python version: {sys.version.split()[0]}")
    return True

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'asyncio',
        'aiohttp', 
        'asyncpg',
        'fastapi',
        'uvicorn',
        'jsonschema',
        'pydantic'
    ]
    
    missing = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing.append(package)
    
    if missing:
        print(f"❌ Missing dependencies: {', '.join(missing)}")
        print("Run: pip install -r requirements.txt")
        return False
    
    print("✓ All required dependencies installed")
    return True

def check_database():
    """Check if database is accessible"""
    database_url = os.getenv('DATABASE_URL', 'postgresql://postgres:password@localhost:5432/poe_dashboard')
    
    try:
        import asyncpg
        
        async def test_connection():
            try:
                conn = await asyncpg.connect(database_url)
                await conn.fetchval("SELECT 1")
                await conn.close()
                return True
            except Exception as e:
                print(f"❌ Database connection failed: {e}")
                return False
        
        result = asyncio.run(test_connection())
        if result:
            print("✓ Database connection successful")
        return result
        
    except ImportError:
        print("❌ asyncpg not installed")
        return False
    except Exception as e:
        print(f"❌ Database check failed: {e}")
        return False

def setup_environment():
    """Setup basic environment variables if not set"""
    env_vars = {
        'DATABASE_URL': 'postgresql://postgres:password@localhost:5432/poe_dashboard',
        'POE_DEFAULT_LEAGUE': 'Mercenaries',
        'LOG_LEVEL': 'INFO',
        'WEB_HOST': '0.0.0.0',
        'WEB_PORT': '8000',
        'ENVIRONMENT': 'development'
    }
    
    set_vars = []
    for var, default in env_vars.items():
        if var not in os.environ:
            os.environ[var] = default
            set_vars.append(f"{var}={default}")
    
    if set_vars:
        print(f"✓ Set environment variables: {', '.join(set_vars)}")
    else:
        print("✓ Environment variables already configured")

def create_directories():
    """Create required directories"""
    dirs = ['logs', 'data', 'database', 'config', 'ingestion', 'monitoring']
    
    for dir_name in dirs:
        Path(dir_name).mkdir(exist_ok=True)
    
    print("✓ Required directories created")

def show_startup_info():
    """Show startup information"""
    print("\n" + "="*60)
    print("🚀 PoE Dashboard Starting Up")
    print("="*60)
    print(f"League: {os.getenv('POE_DEFAULT_LEAGUE', 'Mercenaries')}")
    print(f"Host: {os.getenv('WEB_HOST', '0.0.0.0')}:{os.getenv('WEB_PORT', '8000')}")
    print(f"Environment: {os.getenv('ENVIRONMENT', 'development')}")
    print(f"Log Level: {os.getenv('LOG_LEVEL', 'INFO')}")
    print("="*60)

def main():
    """Main startup function"""
    print("PoE Dashboard - Data Acquisition & Sync System")
    print("=" * 50)
    
    # Check system requirements
    if not check_python_version():
        return 1
    
    # Setup environment
    setup_environment()
    create_directories()
    
    # Check dependencies
    if not check_dependencies():
        print("\n💡 To install dependencies:")
        print("   pip install -r requirements.txt")
        return 1
    
    # Check database (optional for basic testing)
    db_available = check_database()
    if not db_available:
        print("\n💡 Database not available. You can:")
        print("   1. Start with Docker: docker-compose up -d postgres")
        print("   2. Install PostgreSQL locally")
        print("   3. Run basic tests only: python test_basic.py")
        
        response = input("\nContinue without database? (y/N): ").lower()
        if response != 'y':
            return 1
    
    show_startup_info()
    
    # Start the application
    try:
        print("\n🚀 Starting PoE Dashboard...")
        print("Press Ctrl+C to stop\n")
        
        # Import and run the main application
        from main import main as app_main
        app_main()
        
    except KeyboardInterrupt:
        print("\n👋 Shutting down gracefully...")
        return 0
    except ImportError as e:
        print(f"\n❌ Import error: {e}")
        print("Some modules may be missing or have import issues.")
        print("Try running: python test_basic.py")
        return 1
    except Exception as e:
        print(f"\n❌ Startup failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
