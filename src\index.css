@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}

/* Custom focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-poe-accent focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-900;
}

/* Animation utilities */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    transform: translateY(10px); 
    opacity: 0; 
  }
  to { 
    transform: translateY(0); 
    opacity: 1; 
  }
}

/* Custom components */
.card {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700;
}

.btn-primary {
  @apply bg-poe-accent hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-colors focus-ring;
}

.btn-secondary {
  @apply bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 font-medium py-2 px-4 rounded-lg transition-colors focus-ring;
}

.input-field {
  @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus-ring;
}

/* PoE-specific styling */
.chaos-value {
  @apply text-poe-chaos font-semibold;
}

.divine-value {
  @apply text-poe-divine font-semibold;
}

.profit-positive {
  @apply text-green-600 dark:text-green-400;
}

.profit-negative {
  @apply text-red-600 dark:text-red-400;
}

.risk-low {
  @apply text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/20;
}

.risk-medium {
  @apply text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/20;
}

.risk-high {
  @apply text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/20;
}

/* Loading states */
.skeleton {
  @apply animate-pulse bg-gray-200 dark:bg-gray-700 rounded;
}

.loading-dots::after {
  content: '';
  animation: dots 1.5s steps(5, end) infinite;
}

@keyframes dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}
