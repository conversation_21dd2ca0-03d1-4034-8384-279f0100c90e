# PoE Profit AI - Stand-Alone Desktop Edition

A lightning-fast, off-game companion that turns raw market data into actionable trade decisions while keeping players 100% compliant with Grinding Gear Games' third-party tool rules.

![PoE Profit AI](https://img.shields.io/badge/PoE-Profit%20AI-gold?style=for-the-badge)
![Tauri](https://img.shields.io/badge/Tauri-2.0-blue?style=for-the-badge)
![React](https://img.shields.io/badge/React-18-blue?style=for-the-badge)
![Rust](https://img.shields.io/badge/Rust-1.70+-orange?style=for-the-badge)

## ✨ Features

### 🔍 **Quick Price Lookup**
- **Lightning-fast search**: Get item prices in under 200ms
- **Fuzzy matching**: Find items even with typos
- **Comprehensive data**: Chaos/Divine values, trends, liquidity scores
- **Smart caching**: Instant results for recently searched items

### 📈 **Flip Finder**
- **AI-powered analysis**: Machine learning price predictions
- **Risk assessment**: Low/Medium/High risk categorization
- **Profit optimization**: Sort by ROI, profit margins, or confidence
- **Budget management**: Set investment limits and filters

### 💼 **Portfolio Tracker**
- **Real-time valuation**: Track your investments automatically
- **Profit/Loss tracking**: See gains and losses at a glance
- **Multi-league support**: Separate portfolios per league
- **Historical performance**: Track your trading success over time

### 🧪 **League Laboratory**
- **Market analysis**: Deep insights into league economies
- **Meta predictions**: Anticipate market shifts
- **Event tracking**: League start/end notifications
- **Comparative analysis**: Cross-league price comparisons

## 🚀 Quick Start

### Prerequisites
- **Node.js** 18 or later
- **Rust** 1.70 or later
- **Git**

### Development Setup

```bash
# Clone the repository
git clone https://github.com/yourusername/poe-profit-ai.git
cd poe-profit-ai

# Run development setup
chmod +x scripts/dev.sh
./scripts/dev.sh
```

### Manual Setup

```bash
# Install frontend dependencies
npm install

# Install Tauri CLI
cargo install tauri-cli

# Start development server
npm run tauri:dev
```

## 🏗️ Building for Production

```bash
# Run the build script
chmod +x scripts/build.sh
./scripts/build.sh

# Or manually
npm run build
npm run tauri:build
```

## 🛠️ Technology Stack

### Frontend
- **React 18** with TypeScript
- **Tailwind CSS** for styling
- **Zustand** for state management
- **Recharts** for data visualization
- **Lucide React** for icons

### Backend
- **Rust** with Tokio async runtime
- **Tauri 2.0** for desktop framework
- **SQLite** for local data storage
- **reqwest** for HTTP requests
- **serde** for serialization

### Data Sources
- **poe.ninja API** for market data
- **31-minute sync intervals** (as per blueprint)
- **Comprehensive data validation**

## 📊 Performance Targets

- **Cold start**: ≤ 3 seconds
- **Query latency**: ≤ 200ms P95
- **Memory usage**: ≤ 180MB idle
- **Cache hit rate**: > 80%

## 🔒 Security & Compliance

### GGG Compliance
- ✅ **No game client interaction**
- ✅ **No memory reading or injection**
- ✅ **No automation or macros**
- ✅ **Public API data only**

### Privacy
- ✅ **Local data processing**
- ✅ **No telemetry or tracking**
- ✅ **Encrypted local storage**
- ✅ **No personal data transmission**

## 🧪 Testing

```bash
# Run all tests
npm test

# Run Rust tests
cargo test --manifest-path=src-tauri/Cargo.toml

# Run with coverage
npm run test:coverage

# E2E tests
npm run test:e2e
```

## 📝 Scripts

| Command | Description |
|---------|-------------|
| `npm run dev` | Start Vite dev server |
| `npm run build` | Build frontend for production |
| `npm run tauri:dev` | Start Tauri development |
| `npm run tauri:build` | Build Tauri app for production |
| `npm run test` | Run frontend tests |
| `npm run lint` | Lint code |
| `npm run format` | Format code |

## 🗂️ Project Structure

```
poe-profit-ai/
├── src/                    # React frontend
│   ├── components/         # UI components
│   ├── stores/            # Zustand stores
│   ├── types/             # TypeScript types
│   └── utils/             # Utility functions
├── src-tauri/             # Rust backend
│   ├── src/
│   │   ├── commands/      # Tauri commands
│   │   ├── data/          # Database & API
│   │   ├── models/        # Data models
│   │   └── utils/         # Utilities
│   ├── migrations/        # Database migrations
│   └── Cargo.toml         # Rust dependencies
├── scripts/               # Build scripts
└── docs/                  # Documentation
```

## 🔧 Configuration

### Environment Variables
```bash
# Optional: Custom data directory
POE_PROFIT_DATA_DIR=/path/to/data

# Optional: Custom cache size
POE_PROFIT_CACHE_SIZE=1000

# Optional: Custom sync interval (minutes)
POE_PROFIT_SYNC_INTERVAL=31
```

### Database Configuration
The application uses SQLite for local storage with automatic migrations. Data is stored in:
- **Windows**: `%APPDATA%/poe-profit-ai/`
- **macOS**: `~/Library/Application Support/poe-profit-ai/`
- **Linux**: `~/.local/share/poe-profit-ai/`

## 📈 Performance Monitoring

The application includes built-in performance monitoring:
- Query latency tracking
- Memory usage monitoring
- Cache hit rate analysis
- Background sync status

Access performance metrics in **Settings > Performance**.

## 🐛 Troubleshooting

### Common Issues

**App won't start**
```bash
# Check system requirements
node --version  # Should be 18+
cargo --version # Should be 1.70+

# Clear cache and rebuild
rm -rf node_modules dist src-tauri/target
npm install
npm run tauri:build
```

**Slow performance**
- Check available disk space (need 100MB+ free)
- Restart the application
- Clear cache in Settings
- Update to latest version

**Data sync issues**
- Check internet connection
- Force sync in Settings
- Check poe.ninja API status

### Getting Help
- 📖 [Documentation](https://github.com/yourusername/poe-profit-ai/wiki)
- 🐛 [Report Issues](https://github.com/yourusername/poe-profit-ai/issues)
- 💬 [Discord Community](https://discord.gg/your-server)

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Workflow
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

### Code Style
- **Frontend**: ESLint + Prettier
- **Backend**: rustfmt + clippy
- **Commits**: Conventional Commits

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Grinding Gear Games** for Path of Exile
- **poe.ninja** for providing market data API
- **Tauri team** for the amazing desktop framework
- **PoE community** for feedback and support

## 🔮 Roadmap

### v1.1 - Enhanced Analytics
- [ ] Advanced price prediction models
- [ ] Historical trend analysis
- [ ] Market volatility indicators
- [ ] Custom alert system

### v1.2 - Social Features
- [ ] Trade recommendation sharing
- [ ] Community insights
- [ ] League racing features
- [ ] Guild portfolio tracking

### v1.3 - Advanced Tools
- [ ] Crafting profit calculator
- [ ] Atlas strategy optimizer
- [ ] Build cost analyzer
- [ ] Currency exchange optimizer

---

**Built with ❤️ for the Path of Exile community**

*This tool is not affiliated with or endorsed by Grinding Gear Games.*

## 🚀 Features

### Core Data Acquisition
- **31-minute polling cadence** with +1 min offset to avoid thundering herd
- **Parallel fetching** with max 6 concurrent requests and jittered start times
- **Exponential backoff retry** (0.5s, 1s, 2s) with comprehensive error handling
- **Automatic league discovery** from poe.ninja index state
- **Complete endpoint coverage** (31 endpoints: currency + 29 item types)

### Data Quality & Validation
- **Schema validation** with pre-compiled JSON schemas
- **Confidence filtering** (count ≥ 5, listingCount ≥ 10)
- **Currency sanity checks** (chaosEquivalent ∈ [0.5c, 100,000c])
- **Outlier detection** (σ > 4 statistical filtering)
- **Stale data detection** (>90 min threshold with UI warnings)

### Performance & Reliability
- **Respectful API etiquette** (45 req/min global, 5 req/min per endpoint)
- **Automatic backoff** on consecutive failures (60-min intervals)
- **Connection pooling** with async PostgreSQL operations
- **Materialized views** for fast query performance
- **Health monitoring** with comprehensive metrics

### Monitoring & Operations
- **Real-time health checks** (database, API, data quality, pipeline integrity)
- **Fetch metrics tracking** (latency, success rates, error rates)
- **Data quality metrics** (confidence scores, outlier rates, liquidity)
- **RESTful API** for monitoring and control
- **Grafana dashboards** (optional) for visualization

## 📋 Requirements

- Python 3.11+
- PostgreSQL 15+
- Redis 7+ (optional, for caching)
- Docker & Docker Compose (recommended)

## 🛠️ Quick Start

### Option 1: Docker Compose (Recommended)

1. **Clone and setup**:
   ```bash
   git clone <repository-url>
   cd poe-dashboard
   ```

2. **Start all services**:
   ```bash
   docker-compose up -d
   ```

3. **Check health**:
   ```bash
   curl http://localhost:8000/health
   ```

4. **View logs**:
   ```bash
   docker-compose logs -f app
   ```

### Option 2: Local Development

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Setup PostgreSQL**:
   ```bash
   # Create database
   createdb poe_dashboard
   
   # Run schema
   psql poe_dashboard < database/schema.sql
   ```

3. **Configure environment**:
   ```bash
   export DATABASE_URL="postgresql://postgres:password@localhost:5432/poe_dashboard"
   export POE_DEFAULT_LEAGUE="Mercenaries"
   export LOG_LEVEL="INFO"
   ```

4. **Run application**:
   ```bash
   python main.py
   ```

## 🔧 Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `DATABASE_URL` | `postgresql://postgres:password@localhost:5432/poe_dashboard` | PostgreSQL connection string |
| `POE_DEFAULT_LEAGUE` | `Mercenaries` | Default PoE league to track |
| `SCHEDULER_POLL_INTERVAL` | `31` | Polling interval in minutes |
| `API_MAX_CONCURRENT` | `6` | Max concurrent API requests |
| `API_MAX_REQ_PER_MIN` | `45` | Global rate limit (requests/min) |
| `VALIDATION_MIN_LISTING_COUNT` | `10` | Minimum listing count for confidence |
| `LOG_LEVEL` | `INFO` | Logging level (DEBUG, INFO, WARNING, ERROR) |

### Blueprint Configuration

The system implements the comprehensive sync blueprint with these key settings:

- **Polling**: 31-min intervals with +1 min offset
- **Concurrency**: Max 6 parallel requests with ±5s jitter
- **Rate Limits**: 45 req/min global, 5 req/min per endpoint
- **Quality Filters**: count ≥ 5, listingCount ≥ 10, σ ≤ 4
- **Backoff**: 60-min intervals after 3 consecutive failures

## 📊 API Endpoints

### Health & Status
- `GET /health` - Comprehensive health check
- `GET /status` - Application status and metrics
- `GET /metrics` - Detailed performance metrics

### Data Access
- `GET /data/current-prices?league=<league>&limit=<limit>` - Current prices
- `GET /data/top-movers?league=<league>&limit=<limit>` - Price movers

### Control
- `POST /scheduler/pause` - Pause data acquisition
- `POST /scheduler/resume` - Resume data acquisition

## 🗄️ Database Schema

### Core Tables
- **`leagues`** - Active and historical PoE leagues
- **`items`** - Item catalog with stable cross-endpoint join keys
- **`prices`** - Historical price data with calculated metrics
- **`staging_raw`** - Raw JSON staging for ETL pipeline
- **`stats_fetch`** - API fetch metrics for monitoring

### Materialized Views
- **`v_current_price`** - Latest prices with quality filters
- **`v_top_movers`** - Items with significant price changes
- **`v_high_liquidity`** - High-liquidity items
- **`v_outliers`** - Statistical outliers (σ > 4)

## 📈 Monitoring

### Health Checks
The system performs comprehensive health monitoring:

1. **Database Health** - Connectivity, query performance, connection pool
2. **Data Freshness** - Stale data detection (>90 min threshold)
3. **Fetch Performance** - Success rates, latency, endpoint coverage
4. **Data Quality** - Confidence scores, outlier rates, liquidity
5. **Pipeline Integrity** - Processing errors, staging backlog

### Metrics Collection
- **Fetch Metrics**: Latency, success rates, error counts per endpoint
- **Data Metrics**: Record counts, confidence scores, outlier detection
- **System Metrics**: Database performance, connection pool status

### Alerting Thresholds
- **Critical**: Success rate < 99%, data age > 90 min, database down
- **Warning**: High latency > 120ms, low confidence < 70%, processing errors

## 🔍 Data Quality Guardrails

### Confidence Filters
- Discard rows where `count < 5` OR `listingCount < 10`
- Flag outliers where volatility σ > 4
- Currency sanity: `chaosEquivalent ∈ [0.5c, 100,000c]`

### Stale Data Handling
- Banner warning if no successful sync for >90 min
- Automatic fallback to last known good data
- UI indicators for data freshness

## 🚨 Error Handling

### Retry Logic
- **3x retry** with exponential backoff (0.5s, 1s, 2s)
- **Rate limit handling** with automatic delays
- **Circuit breaker** after 3 consecutive failures

### Graceful Degradation
- Continue with partial data on endpoint failures
- Maintain service availability during API issues
- Automatic recovery after backoff periods

## 📝 Logging

Structured logging with configurable levels:
- **INFO**: Normal operations, successful syncs
- **WARNING**: Retries, quality issues, performance concerns
- **ERROR**: Failed requests, processing errors
- **DEBUG**: Detailed request/response data

## 🔒 Security

- **Non-root container** execution
- **Environment-based** configuration
- **Connection pooling** with limits
- **Input validation** and sanitization
- **CORS configuration** for web access

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes with tests
4. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues and questions:
1. Check the health endpoint: `/health`
2. Review application logs
3. Verify database connectivity
4. Check API rate limits

## 🔄 Maintenance

### Regular Tasks
- Monitor health dashboard
- Review error logs
- Update league configurations
- Backup database regularly

### Performance Tuning
- Adjust polling intervals based on load
- Monitor connection pool usage
- Review materialized view refresh frequency
- Optimize database indexes as needed
