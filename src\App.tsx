import { useEffect } from 'react';
import { listen } from '@tauri-apps/api/event';
import { useAppStore } from './stores/appStore';
import { getLeagueList, getSyncStatus } from './utils/tauri';
import Navigation from './components/Navigation/Navigation';
import Dashboard from './components/Dashboard/Dashboard';
import QuickPrice from './components/QuickPrice/QuickPrice';
import FlipFinder from './components/FlipFinder/FlipFinder';
import Portfolio from './components/Portfolio/Portfolio';
import LeagueLab from './components/LeagueLab/LeagueLab';
import Settings from './components/Settings/Settings';
import LoadingSpinner from './components/UI/LoadingSpinner';
import ErrorBoundary from './components/UI/ErrorBoundary';
import SyncStatusBar from './components/UI/SyncStatusBar';

function App() {
  const {
    currentTab,
    isLoading,
    error,
    setLeagues,
    setSyncStatus,
    setLoading,
    setError,
  } = useAppStore();

  useEffect(() => {
    initializeApp();
    setupEventListeners();
  }, []);

  const initializeApp = async () => {
    try {
      setLoading(true);
      
      // Load initial data
      const [leagues, syncStatus] = await Promise.all([
        getLeagueList(),
        getSyncStatus(),
      ]);
      
      setLeagues(leagues);
      setSyncStatus(syncStatus);
      
      setError(undefined);
    } catch (err) {
      console.error('Failed to initialize app:', err);
      setError('Failed to initialize application');
    } finally {
      setLoading(false);
    }
  };

  const setupEventListeners = async () => {
    // Listen for sync events from Tauri backend
    await listen('sync-started', () => {
      console.log('Data sync started');
    });

    await listen('sync-completed', (event) => {
      console.log('Data sync completed:', event.payload);
      // Refresh sync status
      getSyncStatus().then(setSyncStatus).catch(console.error);
    });

    await listen('sync-error', (event) => {
      console.error('Data sync error:', event.payload);
      // Refresh sync status
      getSyncStatus().then(setSyncStatus).catch(console.error);
    });

    await listen('sync-progress', (event) => {
      console.log('Sync progress:', event.payload);
    });
  };

  const renderCurrentTab = () => {
    switch (currentTab) {
      case 'dashboard':
        return <Dashboard />;
      case 'quick-price':
        return <QuickPrice />;
      case 'flip-finder':
        return <FlipFinder />;
      case 'portfolio':
        return <Portfolio />;
      case 'league-lab':
        return <LeagueLab />;
      case 'settings':
        return <Settings />;
      default:
        return <Dashboard />;
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-poe-darker flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="large" />
          <p className="mt-4 text-gray-600 dark:text-gray-400">
            Initializing PoE Profit AI...
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-poe-darker flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="text-red-600 dark:text-red-400 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            Application Error
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="btn-primary"
          >
            Reload Application
          </button>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gray-50 dark:bg-poe-darker">
        {/* Navigation */}
        <Navigation />
        
        {/* Sync Status Bar */}
        <SyncStatusBar />
        
        {/* Main Content */}
        <main className="flex-1 overflow-hidden">
          <div className="h-full">
            {renderCurrentTab()}
          </div>
        </main>
      </div>
    </ErrorBoundary>
  );
}

export default App;
