import os
from typing import Optional
from .connection import DatabaseConnection

DATABASE_URL = os.getenv("DATABASE_URL")
db: Optional[DatabaseConnection] = None

if DATABASE_URL:
    db = DatabaseConnection(DATABASE_URL)
else:
    print("WARNING: DATABASE_URL not set. Database features will be disabled.")

async def init():
    """Initializes the database connection if available."""
    if db:
        await db.connect()

async def close():
    """Closes the database connection if available."""
    if db:
        await db.close()
