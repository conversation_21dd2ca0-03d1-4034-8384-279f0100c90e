import React from 'react';
import { Briefcase, ExternalLink, TrendingUp, TrendingDown } from 'lucide-react';
import { formatCurrency } from '../../utils/tauri';

interface PortfolioCardProps {
  totalValue: number;
  onViewAll: () => void;
}

const PortfolioCard: React.FC<PortfolioCardProps> = ({ totalValue, onViewAll }) => {
  // Mock data for demonstration - in real app this would come from props
  const portfolioStats = {
    totalItems: 12,
    totalInvested: 850,
    profitLoss: totalValue - 850,
    profitLossPct: ((totalValue - 850) / 850) * 100,
  };

  const isProfit = portfolioStats.profitLoss >= 0;

  return (
    <div className="card p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold flex items-center text-gray-900 dark:text-gray-100">
          <Briefcase className="w-5 h-5 mr-2" />
          Portfolio
        </h2>
        <button
          onClick={onViewAll}
          className="text-poe-accent hover:text-blue-600 text-sm font-medium flex items-center"
        >
          View All
          <ExternalLink className="w-4 h-4 ml-1" />
        </button>
      </div>

      <div className="space-y-4">
        {/* Total Value */}
        <div>
          <p className="text-sm text-gray-600 dark:text-gray-400">Total Value</p>
          <p className="text-3xl font-bold chaos-value" data-testid="total-value">
            {formatCurrency(totalValue, 'chaos')}
          </p>
        </div>

        {/* Profit/Loss */}
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400">P&L</p>
            <div className="flex items-center space-x-2">
              {isProfit ? (
                <TrendingUp className="w-4 h-4 text-green-500" />
              ) : (
                <TrendingDown className="w-4 h-4 text-red-500" />
              )}
              <span className={isProfit ? 'profit-positive' : 'profit-negative'}>
                {isProfit ? '+' : ''}{formatCurrency(portfolioStats.profitLoss, 'chaos')}
              </span>
            </div>
          </div>
          
          <div className="text-right">
            <p className="text-sm text-gray-600 dark:text-gray-400">Percentage</p>
            <p className={`font-semibold ${isProfit ? 'profit-positive' : 'profit-negative'}`}>
              {isProfit ? '+' : ''}{portfolioStats.profitLossPct.toFixed(1)}%
            </p>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-200 dark:border-gray-600">
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Items</p>
            <p className="font-semibold text-gray-900 dark:text-gray-100">
              {portfolioStats.totalItems}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Invested</p>
            <p className="font-semibold text-gray-900 dark:text-gray-100">
              {formatCurrency(portfolioStats.totalInvested, 'chaos')}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PortfolioCard;
