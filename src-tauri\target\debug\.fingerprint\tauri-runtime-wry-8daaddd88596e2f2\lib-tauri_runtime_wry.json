{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 2241668132362809309, "path": 4309171813767671541, "deps": [[376837177317575824, "softbuffer", false, 9336303956629900979], [442785307232013896, "tauri_runtime", false, 10032753341566814057], [3150220818285335163, "url", false, 4211341816596351554], [3722963349756955755, "once_cell", false, 6671557954764079245], [4143744114649553716, "raw_window_handle", false, 8005042981042231374], [5986029879202738730, "log", false, 8821711327181341800], [7752760652095876438, "build_script_build", false, 8181990540654839301], [8539587424388551196, "webview2_com", false, 13804335643666345152], [9010263965687315507, "http", false, 4892805295673845999], [11050281405049894993, "tauri_utils", false, 8511169775403936498], [13223659721939363523, "tao", false, 16129216788208323290], [14585479307175734061, "windows", false, 4653572282899060777], [14794439852947137341, "wry", false, 2042651889987496576]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-8daaddd88596e2f2\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}