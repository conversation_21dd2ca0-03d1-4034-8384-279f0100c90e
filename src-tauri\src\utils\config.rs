use std::path::Path<PERSON>uf;
use anyhow::Result;

#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct AppConfig {
    pub database_path: String,
    pub data_dir: PathBuf,
    pub cache_size: usize,
    pub sync_interval_minutes: u64,
}

impl AppConfig {
    pub async fn load() -> Result<Self> {
        // Get app data directory
        let data_dir = if let Some(data_dir) = dirs::data_dir() {
            data_dir.join("poe-profit-ai")
        } else {
            PathBuf::from("./data")
        };
        
        // Ensure data directory exists
        tokio::fs::create_dir_all(&data_dir).await?;
        
        let database_path = data_dir.join("poe_profit.db").to_string_lossy().to_string();
        
        Ok(Self {
            database_path,
            data_dir,
            cache_size: 1000,
            sync_interval_minutes: 31,
        })
    }
}
