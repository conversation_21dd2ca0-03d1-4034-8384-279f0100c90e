{"rustc": 10895048813736897673, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"chrono\", \"default\", \"json\", \"migrate\", \"sqlite\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"time\", \"uuid\"]", "target": 13494433325021527976, "profile": 2225463790103693989, "path": 9936676360200748266, "deps": [[996810380461694889, "sqlx_core", false, 11442842450402954924], [2713742371683562785, "syn", false, 17375468584397320090], [3060637413840920116, "proc_macro2", false, 11906168919180298248], [15733334431800349573, "sqlx_macros_core", false, 6630425988832872611], [17990358020177143287, "quote", false, 299949230227233179]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-macros-8a0e7ae4e096f25a\\dep-lib-sqlx_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}