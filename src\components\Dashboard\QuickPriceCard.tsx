import React, { useState, useCallback, useRef } from 'react';
import { Search, TrendingUp, TrendingDown, Minus } from 'lucide-react';
import { useAppStore } from '../../stores/appStore';
import { quickPriceLookup, searchItemNames, formatCurrency, formatPercentage, getLiquidityColor } from '../../utils/tauri';
import type { PriceResult } from '../../types';
import { debounce } from 'lodash-es';

const QuickPriceCard: React.FC = () => {
  const [query, setQuery] = useState('');
  const [result, setResult] = useState<PriceResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const { currentLeague, setLastQueryTime } = useAppStore();
  const inputRef = useRef<HTMLInputElement>(null);

  // Debounced search for suggestions
  const debouncedSearch = useCallback(
    debounce(async (searchQuery: string) => {
      if (searchQuery.length >= 2) {
        try {
          const items = await searchItemNames(searchQuery, 5);
          setSuggestions(items);
          setShowSuggestions(true);
        } catch (error) {
          console.error('Search failed:', error);
          setSuggestions([]);
        }
      } else {
        setSuggestions([]);
        setShowSuggestions(false);
      }
    }, 300),
    []
  );

  const handleSearch = async (itemName: string) => {
    if (!itemName.trim()) return;
    
    const startTime = performance.now();
    setLoading(true);
    setShowSuggestions(false);
    
    try {
      const priceResult = await quickPriceLookup({
        item_name: itemName,
        league: currentLeague,
      });
      
      setResult(priceResult);
      
      const queryTime = performance.now() - startTime;
      setLastQueryTime(queryTime);
      
    } catch (error) {
      console.error('Price lookup failed:', error);
      setResult(null);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (value: string) => {
    setQuery(value);
    debouncedSearch(value);
  };

  const handleSuggestionClick = (suggestion: string) => {
    setQuery(suggestion);
    setSuggestions([]);
    setShowSuggestions(false);
    handleSearch(suggestion);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch(query);
    } else if (e.key === 'Escape') {
      setShowSuggestions(false);
    }
  };

  const getTrendIcon = (trend: number | null | undefined) => {
    if (!trend) return <Minus className="w-4 h-4 text-gray-400" />;
    if (trend > 0) return <TrendingUp className="w-4 h-4 text-green-500" />;
    return <TrendingDown className="w-4 h-4 text-red-500" />;
  };

  const getTrendColor = (trend: number | null | undefined) => {
    if (!trend) return 'text-gray-400';
    return trend > 0 ? 'text-green-500' : 'text-red-500';
  };

  return (
    <div className="card p-6" data-testid="quick-price-card">
      <h2 className="text-xl font-semibold mb-4 flex items-center text-gray-900 dark:text-gray-100">
        <Search className="w-5 h-5 mr-2" />
        Quick Price
      </h2>
      
      {/* Search Input */}
      <div className="relative mb-4">
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => handleInputChange(e.target.value)}
          onKeyPress={handleKeyPress}
          onFocus={() => suggestions.length > 0 && setShowSuggestions(true)}
          onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
          placeholder="Type or paste item name..."
          className="input-field"
          data-testid="price-search-input"
        />
        
        {/* Suggestions Dropdown */}
        {showSuggestions && suggestions.length > 0 && (
          <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-48 overflow-y-auto">
            {suggestions.map((suggestion, index) => (
              <button
                key={index}
                onClick={() => handleSuggestionClick(suggestion)}
                className="w-full px-4 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-600 first:rounded-t-lg last:rounded-b-lg text-gray-900 dark:text-gray-100"
              >
                {suggestion}
              </button>
            ))}
          </div>
        )}
      </div>
      
      {/* Search Button */}
      <button
        onClick={() => handleSearch(query)}
        disabled={loading || !query.trim()}
        className="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
        data-testid="price-search-button"
      >
        {loading ? (
          <span className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            Searching...
          </span>
        ) : (
          'Get Price'
        )}
      </button>
      
      {/* Results */}
      {result && (
        <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg animate-slide-up" data-testid="price-result">
          <h3 className="font-semibold text-lg mb-3 text-gray-900 dark:text-gray-100">
            {result.item_name}
          </h3>
          
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Chaos Value</p>
              <p className="text-2xl font-bold chaos-value" data-testid="chaos-value">
                {formatCurrency(result.chaos_value, 'chaos')}
              </p>
            </div>
            
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Divine Value</p>
              <p className="text-2xl font-bold divine-value" data-testid="divine-value">
                {formatCurrency(result.divine_value, 'divine')}
              </p>
            </div>
          </div>
          
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center">
              {getTrendIcon(result.trend_7d)}
              <span className={`ml-1 text-sm ${getTrendColor(result.trend_7d)}`}>
                {formatPercentage(result.trend_7d)} (7d)
              </span>
            </div>
            
            <div className="text-sm">
              <span className="text-gray-600 dark:text-gray-400">Liquidity: </span>
              <span className={getLiquidityColor(result.liquidity)}>
                {result.liquidity}
              </span>
            </div>
          </div>
          
          <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>
              Listings: {result.listing_count}
            </span>
            <span>
              Confidence: {(result.confidence * 100).toFixed(0)}%
            </span>
          </div>
          
          <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
            Updated: {new Date(result.last_updated).toLocaleString()}
          </div>
        </div>
      )}
    </div>
  );
};

export default QuickPriceCard;
