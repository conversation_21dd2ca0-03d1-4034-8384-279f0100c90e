use tauri::State;
use chrono::Utc;
use sysinfo::{System, SystemExt, ProcessExt};

use crate::{AppState, models::*};

#[tauri::command]
pub async fn get_app_info(
    state: State<'_, AppState>,
) -> Result<AppInfo, String> {
    let uptime_seconds = state.startup_time.elapsed().as_secs();
    let startup_time_ms = 3000; // Placeholder - would be calculated during startup
    
    // Get database size (simplified)
    let database_size_mb = 10.0; // Placeholder
    
    let cache_size = state.cache_manager.read().await.get_cache_size();
    
    Ok(AppInfo {
        version: env!("CARGO_PKG_VERSION").to_string(),
        startup_time_ms,
        uptime_seconds,
        database_size_mb,
        cache_size,
    })
}

#[tauri::command]
pub async fn get_performance_metrics(
    state: State<'_, AppState>,
) -> Result<PerformanceMetrics, String> {
    let cache_stats = state.cache_manager.read().await.get_stats().await;
    
    // Get system metrics
    let mut system = System::new_all();
    system.refresh_all();
    
    let memory_usage_mb = if let Some(process) = system.processes_by_exact_name("poe-profit-ai").next() {
        process.memory() as f64 / 1024.0 / 1024.0
    } else {
        0.0
    };
    
    let cpu_usage_percent = if let Some(process) = system.processes_by_exact_name("poe-profit-ai").next() {
        process.cpu_usage() as f64
    } else {
        0.0
    };
    
    Ok(PerformanceMetrics {
        avg_query_time_ms: 50.0, // Placeholder - would track actual query times
        cache_hit_rate: cache_stats.hit_rate(),
        memory_usage_mb,
        cpu_usage_percent,
        active_connections: 1,
        last_updated: Utc::now(),
    })
}

#[tauri::command]
pub async fn force_data_sync(
    state: State<'_, AppState>,
) -> Result<String, String> {
    let mut sync_scheduler = state.sync_scheduler.write().await;

    if let Some(scheduler) = sync_scheduler.as_mut() {
        scheduler.trigger_manual_sync().await
            .map_err(|e| format!("Sync failed: {}", e))?;
        
        Ok("Data sync completed successfully".to_string())
    } else {
        Err("Sync scheduler not available".to_string())
    }
}

#[tauri::command]
pub async fn get_sync_status(
    state: State<'_, AppState>,
) -> Result<SyncStatus, String> {
    let sync_scheduler = state.sync_scheduler.read().await;

    if let Some(scheduler) = sync_scheduler.as_ref() {
        Ok(scheduler.get_sync_status())
    } else {
        Ok(SyncStatus {
            is_syncing: false,
            last_sync: None,
            last_error: Some("Scheduler not initialized".to_string()),
            items_synced: 0,
            sync_duration_ms: 0,
            next_sync_eta: None,
        })
    }
}

#[tauri::command]
pub async fn test_database_performance(
    state: State<'_, AppState>,
) -> Result<String, String> {
    let db_manager = &state.db_manager;

    // Create test data
    let test_items: Vec<(PoeNinjaItem, String)> = (0..100).map(|i| {
        (PoeNinjaItem {
            currency_type_name: Some(format!("Test Currency {}", i)),
            chaos_equivalent: Some(10.0 + i as f64),
            divine_equivalent: Some(1.0 + i as f64 * 0.1),
            listing_count: Some(50 + i),
            name: Some(format!("Test Item {}", i)),
            type_line: Some("Test Type".to_string()),
            base_type: Some("Test Base".to_string()),
            icon: Some("test_icon.png".to_string()),
            details_id: Some(format!("test_item_{}", i)),
        }, "test_category".to_string())
    }).collect();

    // Test old method (individual transactions)
    let start_old = std::time::Instant::now();
    let league_id = db_manager.upsert_league("TestLeague", "Test League").await
        .map_err(|e| format!("Failed to create test league: {}", e))?;

    let mut old_method_count = 0;
    for (item, category) in &test_items[0..10] { // Test with just 10 items for old method
        match db_manager.upsert_item(item, league_id, category).await {
            Ok(item_id) => {
                if db_manager.insert_price(item_id, league_id, item).await.is_ok() {
                    old_method_count += 1;
                }
            }
            Err(_) => {}
        }
    }
    let old_duration = start_old.elapsed();

    // Test new batch method
    let start_new = std::time::Instant::now();
    let new_method_count = db_manager.batch_upsert_items_with_prices(&test_items, league_id).await
        .map_err(|e| format!("Batch method failed: {}", e))?;
    let new_duration = start_new.elapsed();

    // Test subquery method
    let start_subquery = std::time::Instant::now();
    let subquery_count = db_manager.batch_upsert_items_with_prices_subquery(&test_items, league_id).await
        .map_err(|e| format!("Subquery method failed: {}", e))?;
    let subquery_duration = start_subquery.elapsed();

    let speedup_batch = old_duration.as_millis() as f64 / new_duration.as_millis() as f64 * (test_items.len() as f64 / 10.0);
    let speedup_subquery = old_duration.as_millis() as f64 / subquery_duration.as_millis() as f64 * (test_items.len() as f64 / 10.0);

    Ok(format!(
        "Performance Test Results:\n\
        Old Method (10 items): {} items in {:?} ({:.1} items/sec)\n\
        Batch Method ({} items): {} items in {:?} ({:.1} items/sec)\n\
        Subquery Method ({} items): {} items in {:?} ({:.1} items/sec)\n\
        Speedup (Batch): {:.1}x faster\n\
        Speedup (Subquery): {:.1}x faster",
        old_method_count, old_duration, old_method_count as f64 / old_duration.as_secs_f64(),
        test_items.len(), new_method_count, new_duration, new_method_count as f64 / new_duration.as_secs_f64(),
        test_items.len(), subquery_count, subquery_duration, subquery_count as f64 / subquery_duration.as_secs_f64(),
        speedup_batch, speedup_subquery
    ))
}
