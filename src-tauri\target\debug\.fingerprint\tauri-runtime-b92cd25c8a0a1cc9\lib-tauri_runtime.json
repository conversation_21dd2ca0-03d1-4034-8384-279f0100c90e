{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 2241668132362809309, "path": 2397774075986403827, "deps": [[442785307232013896, "build_script_build", false, 15805198089873842148], [3150220818285335163, "url", false, 4211341816596351554], [4143744114649553716, "raw_window_handle", false, 8005042981042231374], [7606335748176206944, "dpi", false, 3661985441037025414], [9010263965687315507, "http", false, 4892805295673845999], [9689903380558560274, "serde", false, 1473350663965462290], [10806645703491011684, "thiserror", false, 2466156708347583921], [11050281405049894993, "tauri_utils", false, 8511169775403936498], [14585479307175734061, "windows", false, 4653572282899060777], [15367738274754116744, "serde_json", false, 1469785836717808546], [16727543399706004146, "cookie", false, 586551450754422686]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-b92cd25c8a0a1cc9\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}