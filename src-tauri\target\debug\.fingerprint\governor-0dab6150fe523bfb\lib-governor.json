{"rustc": 10895048813736897673, "features": "[\"dashmap\", \"default\", \"futures\", \"futures-timer\", \"jitter\", \"quanta\", \"rand\", \"std\"]", "declared_features": "[\"dashmap\", \"default\", \"futures\", \"futures-timer\", \"jitter\", \"no_std\", \"quanta\", \"rand\", \"std\"]", "target": 12176594082583514360, "profile": 2241668132362809309, "path": 6100028003450821483, "deps": [[2706460456408817945, "futures", false, 7427721853870107371], [2828590642173593838, "cfg_if", false, 11883062172440061561], [3666196340704888985, "smallvec", false, 7107425737656729936], [3958489542916937055, "portable_atomic", false, 9558080767409780702], [4495526598637097934, "parking_lot", false, 5252594270913081991], [5364813825765636762, "dashmap", false, 15046453737930453362], [6613228103912905794, "nonzero_ext", false, 13914482329869690252], [8140693133181067772, "futures_timer", false, 7559695787843484910], [9108455738564554921, "quanta", false, 11992459856676628280], [11229226719215837303, "spinning_top", false, 13760794072684453427], [13208667028893622512, "rand", false, 16836568625260154127], [13410323020980513144, "no_std_compat", false, 10677279245667848247]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\governor-0dab6150fe523bfb\\dep-lib-governor", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}