from typing import Optional
from . import db

async def get_item_price(item_name: str, league: str = "Standard") -> Optional[dict]:
    """
    Queries v_current_price for a case-insensitive item_name.
    Returns a dict with both chaos_value and divine_value if found, else None.
    """
    query = """
        SELECT chaos_value, divine_value
        FROM v_current_price
        WHERE item_name ILIKE $1 AND league = $2
        ORDER BY ts DESC
        LIMIT 1;
    """
    async with db.pool.acquire() as connection:
        row = await connection.fetchrow(query, item_name, league)
    return dict(row) if row else None

async def convert_price(item_row: dict, to_currency: str) -> Optional[float]:
    """
    Converts the price of an item to the specified currency.
    """
    if to_currency.lower() == 'chaos':
        return item_row.get('chaos_value')
    elif to_currency.lower() == 'divine':
        return item_row.get('divine_value')
    return None