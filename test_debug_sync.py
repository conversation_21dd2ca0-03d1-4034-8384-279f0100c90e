#!/usr/bin/env python3
"""
Test script to verify debug logging for poe.ninja sync
"""

import asyncio
import logging
import sys
from datetime import datetime

# Configure debug logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('logs/debug_sync_test.log')
    ]
)

# Reduce noise from external libraries
logging.getLogger("asyncpg").setLevel(logging.WARNING)
logging.getLogger("aiohttp").setLevel(logging.WARNING)
logging.getLogger("urllib3").setLevel(logging.WARNING)

logger = logging.getLogger(__name__)

async def test_debug_sync():
    """Test the debug logging for sync operations"""
    try:
        logger.info("Starting debug sync test...")
        
        # Import after logging setup
        from poe_ninja_api import PoeNinjaAPI
        
        # Test API client with debug logging
        logger.debug("Creating PoeNinjaAPI client...")
        api_client = PoeNinjaAPI(league="Standard")
        
        logger.debug("Initializing API client...")
        await api_client.initialize()
        
        logger.info("Testing single endpoint fetch...")
        currency_data = await api_client.get_currency_data()
        
        if currency_data:
            logger.info(f"Successfully fetched currency data: {len(currency_data.get('lines', []))} items")
            logger.debug(f"Currency data keys: {list(currency_data.keys())}")
        else:
            logger.warning("No currency data received")
        
        logger.info("Testing parallel endpoint fetch...")
        all_data = await api_client.fetch_all_endpoints()
        
        logger.info(f"Parallel fetch completed: {len(all_data)} endpoints")
        for endpoint, data in all_data.items():
            if data and 'lines' in data:
                logger.debug(f"Endpoint {endpoint}: {len(data['lines'])} items")
            else:
                logger.debug(f"Endpoint {endpoint}: No data")
        
        # Get metrics summary
        metrics = api_client.get_metrics_summary()
        if metrics:
            logger.info("Metrics summary:")
            for key, value in metrics.items():
                logger.info(f"  {key}: {value}")
        
        await api_client.close()
        logger.info("Debug sync test completed successfully")
        
    except Exception as e:
        logger.error(f"Debug sync test failed: {e}")
        logger.debug(f"Exception details: {type(e).__name__}: {str(e)}")
        raise

if __name__ == "__main__":
    asyncio.run(test_debug_sync())
