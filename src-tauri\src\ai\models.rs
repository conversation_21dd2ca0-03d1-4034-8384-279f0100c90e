use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct PricePrediction {
    pub item_id: i64,
    pub predicted_price: f64,
    pub confidence: f64,
    pub prediction_horizon_hours: u32,
    pub model_version: String,
    pub features_used: Vec<String>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct MarketFeatures {
    pub item_id: i64,
    pub current_price: f64,
    pub price_7d_avg: f64,
    pub price_24h_change: f64,
    pub price_7d_change: f64,
    pub volatility_7d: f64,
    pub listing_count: i32,
    pub listing_count_change_24h: f64,
    pub volume_7d: f64,
    pub market_cap_rank: Option<i32>,
    pub league_age_days: f64,
    pub is_weekend: bool,
    pub hour_of_day: u8,
    pub day_of_week: u8,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ModelMetrics {
    pub model_name: String,
    pub accuracy: f64,
    pub precision: f64,
    pub recall: f64,
    pub f1_score: f64,
    pub mean_absolute_error: f64,
    pub root_mean_square_error: f64,
    pub training_samples: usize,
    pub last_trained: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FlipPrediction {
    pub item_id: i64,
    pub item_name: String,
    pub current_price: f64,
    pub predicted_price_1h: f64,
    pub predicted_price_6h: f64,
    pub predicted_price_24h: f64,
    pub profit_potential_1h: f64,
    pub profit_potential_6h: f64,
    pub profit_potential_24h: f64,
    pub confidence_score: f64,
    pub risk_score: f64,
    pub liquidity_score: f64,
    pub recommended_action: FlipAction,
    pub reasoning: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FlipAction {
    Buy,
    Sell,
    Hold,
    Avoid,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataQualityReport {
    pub total_items_checked: usize,
    pub outliers_detected: usize,
    pub missing_data_points: usize,
    pub data_freshness_score: f64,
    pub confidence_distribution: Vec<f64>,
    pub quality_issues: Vec<QualityIssue>,
    pub overall_score: f64,
    pub generated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QualityIssue {
    pub item_id: i64,
    pub item_name: String,
    pub issue_type: QualityIssueType,
    pub severity: IssueSeverity,
    pub description: String,
    pub suggested_action: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum QualityIssueType {
    PriceOutlier,
    StaleData,
    InconsistentListing,
    SuspiciousVolume,
    MissingData,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum IssueSeverity {
    Low,
    Medium,
    High,
    Critical,
}

impl Default for FlipAction {
    fn default() -> Self {
        FlipAction::Hold
    }
}
