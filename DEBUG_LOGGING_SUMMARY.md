# Debug Logging for PoE.ninja Sync

This document summarizes all the debug logging that has been added to the PoE.ninja synchronization system.

## Overview

Debug logging has been added throughout the sync pipeline to provide detailed visibility into:
- API request/response details
- Rate limiting behavior
- Concurrent fetch operations
- Data processing performance
- Error handling and retry logic
- Database operations

## Python Side Debug Logs

### 1. poe_ninja_api.py

**Enhanced _fetch_with_retry method:**
- Debug logs for rate limiting state
- HTTP request/response details (status, headers, content-length)
- Response structure analysis
- Retry attempt details with jitter timing
- Timeout and exception handling details

**Enhanced fetch_all_endpoints method:**
- Endpoint configuration logging
- Parallel execution timing
- Individual endpoint success/failure tracking
- Performance metrics (items/sec)

### 2. sync_data.py

**Enhanced run_data_sync function:**
- Database and environment configuration
- Component initialization timing
- Fetch phase timing and results
- Validation phase details
- ETL processing performance
- Cleanup operation timing
- Overall sync performance breakdown

### 3. ingestion/fetch_task.py

**Enhanced fetch_all_data method:**
- Fetch configuration details
- Jitter timing for each endpoint
- Parallel execution monitoring
- Success/failure tracking per endpoint
- Performance metrics

**Enhanced retry logic:**
- Individual retry attempts
- Exception details
- Timing for each endpoint

## Rust Side Debug Logs

### 1. src-tauri/src/scheduler.rs

**Enhanced run_rust_data_sync function:**
- Sync configuration logging
- League upsert details
- Fetch timing and results
- Database storage performance
- Item processing rates
- Error details for failed operations

### 2. src-tauri/src/data/fetcher.rs

**Enhanced fetch_all_data method:**
- Endpoint configuration
- Rate limiting behavior
- Individual request timing
- Response analysis
- Overall performance metrics

**Enhanced fetch_endpoint_data method:**
- Rate limiter wait times
- HTTP request/response details
- Retry logic with timing
- Error response body logging

## Logging Configuration

### Python Configuration

**sync_data.py:**
```python
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('logs/sync.log')
    ]
)
```

**main.py:**
- DEBUG level enabled in development environment
- Specific debug logging for sync modules
- External library noise reduction

### Rust Configuration

**src-tauri/src/main.rs:**
```rust
env_logger::Builder::from_default_env()
    .filter_level(log::LevelFilter::Debug)
    .init();
```

## Log Files

- `logs/sync.log` - Python sync operations
- `logs/poe_dashboard.log` - Main application logs
- `logs/debug_sync_test.log` - Test script logs

## Testing Debug Logs

Use the provided test script:
```bash
python test_debug_sync.py
```

This will:
- Test single endpoint fetch with debug logging
- Test parallel endpoint fetch
- Display metrics summary
- Log all debug information to console and file

## Key Debug Information

### Rate Limiting
- Current request counts vs limits
- Rate limit wait times
- Retry-After header values

### Performance
- Request latency per endpoint
- Items processed per second
- Database operation timing
- Overall sync duration breakdown

### Error Handling
- Exception types and details
- Retry attempt progression
- HTTP error response bodies
- Network timeout details

### Data Quality
- Items count per endpoint
- Response structure validation
- Missing or empty responses

## Environment Variables

Set `LOG_LEVEL=DEBUG` in your environment or `.env` file to enable debug logging in production.

For Rust logging, set `RUST_LOG=debug` environment variable.
