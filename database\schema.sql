-- PoE Dashboard Database Schema
-- Implements the comprehensive data acquisition blueprint

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Leagues table with active tracking
CREATE TABLE leagues (
    id SERIAL PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(100) NOT NULL UNIQUE,
    display_name VA<PERSON>HAR(100) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    first_seen TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_seen TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Items table with stable cross-endpoint join key
CREATE TABLE items (
    id SERIAL PRIMARY KEY,
    details_id TEXT NOT NULL, -- Stable cross-endpoint join key from blueprint
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100) NOT NULL,
    category VARCHAR(100),
    base_type VARCHAR(255),
    icon_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(details_id, type)
);

-- Enhanced prices table with blueprint requirements
CREATE TABLE prices (
    id SERIAL PRIMARY KEY,
    item_id INTEGER REFERENCES items(id) ON DELETE CASCADE,
    league_id INTEGER REFERENCES leagues(id) ON DELETE CASCADE,
    
    -- Core pricing data
    chaos_value NUMERIC(15,4),
    divine_value NUMERIC(15,4),
    
    -- Currency arbitrage spreads from blueprint
    pay_value NUMERIC(15,4),
    receive_value NUMERIC(15,4),
    
    -- Market data
    listing_count INTEGER DEFAULT 0,
    
    -- Sparkline data with quick min/max scan capability
    sparkline_data NUMERIC(15,4)[], -- 7-day price history
    sparkline_low NUMERIC(15,4),    -- Quick min scan without deserializing
    sparkline_high NUMERIC(15,4),   -- Quick max scan without deserializing
    
    -- Calculated metrics
    volatility NUMERIC(8,4),        -- σ (sigma) from blueprint
    momentum_7d NUMERIC(8,4),       -- Δ7d from blueprint
    liquidity_score NUMERIC(8,4),   -- listingCount / maxListingCount(type)
    
    -- Confidence and quality filters
    confidence_score NUMERIC(4,2) DEFAULT 1.0,
    is_outlier BOOLEAN DEFAULT false,
    
    -- Timestamps
    snapshot_time TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(item_id, league_id, snapshot_time)
);

-- Staging table for raw JSON data
CREATE TABLE staging_raw (
    id SERIAL PRIMARY KEY,
    league_name VARCHAR(100) NOT NULL,
    endpoint_type VARCHAR(50) NOT NULL,
    raw_data JSONB NOT NULL,
    fetched_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    processed BOOLEAN DEFAULT false,
    processing_error TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Stats fetch table for ops monitoring
CREATE TABLE stats_fetch (
    id SERIAL PRIMARY KEY,
    endpoint VARCHAR(100) NOT NULL,
    league_name VARCHAR(100) NOT NULL,
    status INTEGER NOT NULL,
    latency_ms NUMERIC(8,2),
    etag VARCHAR(255),
    rows_ingested INTEGER DEFAULT 0,
    error_message TEXT,
    fetch_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Materialized view for current prices
CREATE MATERIALIZED VIEW v_current_price AS
SELECT DISTINCT ON (p.item_id, p.league_id)
    i.details_id,
    i.name,
    i.type,
    i.category,
    l.name as league_name,
    p.chaos_value,
    p.divine_value,
    p.listing_count,
    p.volatility,
    p.momentum_7d,
    p.liquidity_score,
    p.confidence_score,
    p.snapshot_time
FROM prices p
JOIN items i ON p.item_id = i.id
JOIN leagues l ON p.league_id = l.id
WHERE l.is_active = true
  AND p.confidence_score >= 0.7  -- Quality filter from blueprint
  AND p.listing_count >= 10       -- Liquidity filter from blueprint
ORDER BY p.item_id, p.league_id, p.snapshot_time DESC;

-- Materialized view for top movers
CREATE MATERIALIZED VIEW v_top_movers AS
SELECT 
    i.details_id,
    i.name,
    i.type,
    l.name as league_name,
    p.chaos_value,
    p.momentum_7d,
    p.listing_count,
    p.snapshot_time,
    CASE 
        WHEN p.momentum_7d > 20 THEN 'Rising Fast'
        WHEN p.momentum_7d > 5 THEN 'Rising'
        WHEN p.momentum_7d < -20 THEN 'Falling Fast'
        WHEN p.momentum_7d < -5 THEN 'Falling'
        ELSE 'Stable'
    END as trend_category
FROM prices p
JOIN items i ON p.item_id = i.id
JOIN leagues l ON p.league_id = l.id
WHERE l.is_active = true
  AND p.confidence_score >= 0.7
  AND p.listing_count >= 10
  AND ABS(p.momentum_7d) >= 5
  AND p.snapshot_time >= CURRENT_TIMESTAMP - INTERVAL '1 hour'
ORDER BY ABS(p.momentum_7d) DESC
LIMIT 100;

-- Materialized view for high liquidity items
CREATE MATERIALIZED VIEW v_high_liquidity AS
SELECT 
    i.details_id,
    i.name,
    i.type,
    l.name as league_name,
    p.chaos_value,
    p.divine_value,
    p.listing_count,
    p.liquidity_score,
    p.snapshot_time
FROM prices p
JOIN items i ON p.item_id = i.id
JOIN leagues l ON p.league_id = l.id
WHERE l.is_active = true
  AND p.confidence_score >= 0.8
  AND p.liquidity_score >= 0.7  -- High liquidity threshold
  AND p.snapshot_time >= CURRENT_TIMESTAMP - INTERVAL '1 hour'
ORDER BY p.liquidity_score DESC, p.listing_count DESC
LIMIT 200;

-- Materialized view for outliers detection
CREATE MATERIALIZED VIEW v_outliers AS
SELECT 
    i.details_id,
    i.name,
    i.type,
    l.name as league_name,
    p.chaos_value,
    p.volatility,
    p.listing_count,
    p.snapshot_time,
    'High Volatility' as outlier_reason
FROM prices p
JOIN items i ON p.item_id = i.id
JOIN leagues l ON p.league_id = l.id
WHERE l.is_active = true
  AND p.volatility > 4  -- σ > 4 from blueprint
  AND p.listing_count >= 5
  AND p.snapshot_time >= CURRENT_TIMESTAMP - INTERVAL '1 hour'
ORDER BY p.volatility DESC
LIMIT 50;

-- Indexes for performance
CREATE INDEX idx_prices_item_league_time ON prices(item_id, league_id, snapshot_time DESC);
CREATE INDEX idx_prices_snapshot_time ON prices(snapshot_time DESC);
CREATE INDEX idx_prices_chaos_value ON prices(chaos_value DESC) WHERE chaos_value IS NOT NULL;
CREATE INDEX idx_prices_momentum ON prices(momentum_7d DESC) WHERE momentum_7d IS NOT NULL;
CREATE INDEX idx_prices_liquidity ON prices(liquidity_score DESC) WHERE liquidity_score IS NOT NULL;
CREATE INDEX idx_items_details_id ON items(details_id);
CREATE INDEX idx_items_type ON items(type);
CREATE INDEX idx_leagues_active ON leagues(is_active) WHERE is_active = true;
CREATE INDEX idx_staging_processed ON staging_raw(processed, fetched_at) WHERE processed = false;
CREATE INDEX idx_stats_fetch_time ON stats_fetch(fetch_time DESC);

-- GIN index for JSONB operations
CREATE INDEX idx_staging_raw_data ON staging_raw USING GIN(raw_data);

-- Trigger to update updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_leagues_updated_at BEFORE UPDATE ON leagues
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_items_updated_at BEFORE UPDATE ON items
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to refresh materialized views
CREATE OR REPLACE FUNCTION refresh_price_views()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW v_current_price;
    REFRESH MATERIALIZED VIEW v_top_movers;
    REFRESH MATERIALIZED VIEW v_high_liquidity;
    REFRESH MATERIALIZED VIEW v_outliers;
END;
$$ LANGUAGE plpgsql;

-- Insert default leagues
INSERT INTO leagues (name, display_name, is_active) VALUES
('Standard', 'Standard', true),
('Hardcore', 'Hardcore', true),
('Mercenaries', 'Mercenaries', true)
ON CONFLICT (name) DO NOTHING;

-- Comments for documentation
COMMENT ON TABLE leagues IS 'Active and historical PoE leagues with garbage collection tracking';
COMMENT ON TABLE items IS 'Item catalog with stable cross-endpoint join keys';
COMMENT ON TABLE prices IS 'Historical price data with calculated metrics and quality filters';
COMMENT ON TABLE staging_raw IS 'Raw JSON staging area for ETL pipeline';
COMMENT ON TABLE stats_fetch IS 'API fetch metrics for operational monitoring';
COMMENT ON COLUMN prices.details_id IS 'Stable identifier for cross-endpoint joins';
COMMENT ON COLUMN prices.pay_value IS 'Currency arbitrage pay value';
COMMENT ON COLUMN prices.receive_value IS 'Currency arbitrage receive value';
COMMENT ON COLUMN prices.sparkline_low IS 'Quick min scan without JSON deserialization';
COMMENT ON COLUMN prices.sparkline_high IS 'Quick max scan without JSON deserialization';
COMMENT ON COLUMN prices.volatility IS 'Price volatility (sigma) for outlier detection';
COMMENT ON COLUMN prices.momentum_7d IS '7-day price momentum for trend analysis';
COMMENT ON COLUMN prices.liquidity_score IS 'Normalized liquidity score (listingCount / maxListingCount)';
