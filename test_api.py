#!/usr/bin/env python3
"""
Simple test script to verify poe.ninja API connectivity and response format
"""

import asyncio
import aiohttp
import json
from datetime import datetime

async def test_poe_ninja_api():
    """Test the poe.ninja API endpoints"""
    
    base_url = "https://poe.ninja/api/data"
    league = "Settlers"  # Current active league
    
    # Test endpoints from the documentation
    test_endpoints = [
        ("currencyoverview", "Currency"),
        ("currencyoverview", "Fragment"),
        ("itemoverview", "Oil"),
        ("itemoverview", "Scarab"),
        ("itemoverview", "DivinationCard"),
    ]
    
    print(f"Testing poe.ninja API for league: {league}")
    print("=" * 50)
    
    async with aiohttp.ClientSession() as session:
        for category, endpoint in test_endpoints:
            url = f"{base_url}/{category}?league={league}&type={endpoint}"
            print(f"\nTesting: {category}/{endpoint}")
            print(f"URL: {url}")
            
            try:
                async with session.get(url, timeout=10) as response:
                    print(f"Status: {response.status}")
                    
                    if response.status == 200:
                        data = await response.json()
                        lines_count = len(data.get('lines', []))
                        currency_details_count = len(data.get('currencyDetails', []))
                        
                        print(f"✅ Success: {lines_count} items")
                        print(f"   Currency details: {currency_details_count}")
                        print(f"   Response keys: {list(data.keys())}")
                        
                        # Show first item as example
                        if data.get('lines'):
                            first_item = data['lines'][0]
                            print(f"   First item keys: {list(first_item.keys())}")
                            if 'name' in first_item:
                                print(f"   First item name: {first_item['name']}")
                            elif 'currencyTypeName' in first_item:
                                print(f"   First item name: {first_item['currencyTypeName']}")
                    else:
                        error_text = await response.text()
                        print(f"❌ Error: HTTP {response.status}")
                        print(f"   Response: {error_text[:200]}...")
                        
            except asyncio.TimeoutError:
                print("❌ Timeout error")
            except Exception as e:
                print(f"❌ Exception: {e}")
            
            # Small delay between requests
            await asyncio.sleep(0.5)
    
    print("\n" + "=" * 50)
    print("API test completed!")

if __name__ == "__main__":
    asyncio.run(test_poe_ninja_api())
