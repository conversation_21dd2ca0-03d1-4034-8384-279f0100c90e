"""
Configuration Management
=======================
Centralized configuration for the PoE dashboard application
"""

import os
from typing import Optional
from dataclasses import dataclass
from pathlib import Path

@dataclass
class DatabaseConfig:
    """Database configuration"""
    url: str
    min_connections: int = 5
    max_connections: int = 20
    
    @classmethod
    def from_env(cls) -> 'DatabaseConfig':
        """Create database config from environment variables"""
        url = os.getenv('DATABASE_URL', 'postgresql://postgres:password@localhost:5432/poe_dashboard')
        min_conn = int(os.getenv('DB_MIN_CONNECTIONS', '5'))
        max_conn = int(os.getenv('DB_MAX_CONNECTIONS', '20'))
        
        return cls(
            url=url,
            min_connections=min_conn,
            max_connections=max_conn
        )

@dataclass
class APIConfig:
    """API configuration"""
    base_url: str = "https://poe.ninja/api/data"
    default_league: str = "Mercenaries"
    max_concurrent_requests: int = 6
    request_timeout_seconds: int = 30
    max_requests_per_minute: int = 45
    max_endpoint_requests_per_minute: int = 5
    
    @classmethod
    def from_env(cls) -> 'APIConfig':
        """Create API config from environment variables"""
        return cls(
            base_url=os.getenv('POE_NINJA_BASE_URL', cls.base_url),
            default_league=os.getenv('POE_DEFAULT_LEAGUE', cls.default_league),
            max_concurrent_requests=int(os.getenv('API_MAX_CONCURRENT', '6')),
            request_timeout_seconds=int(os.getenv('API_TIMEOUT', '30')),
            max_requests_per_minute=int(os.getenv('API_MAX_REQ_PER_MIN', '45')),
            max_endpoint_requests_per_minute=int(os.getenv('API_MAX_ENDPOINT_REQ_PER_MIN', '5'))
        )

@dataclass
class SchedulerConfig:
    """Scheduler configuration"""
    poll_interval_minutes: int = 31
    offset_minutes: int = 1
    jitter_seconds: int = 5
    max_consecutive_failures: int = 3
    backoff_interval_minutes: int = 60
    auto_restart: bool = True
    
    @classmethod
    def from_env(cls) -> 'SchedulerConfig':
        """Create scheduler config from environment variables"""
        return cls(
            poll_interval_minutes=int(os.getenv('SCHEDULER_POLL_INTERVAL', '31')),
            offset_minutes=int(os.getenv('SCHEDULER_OFFSET', '1')),
            jitter_seconds=int(os.getenv('SCHEDULER_JITTER', '5')),
            max_consecutive_failures=int(os.getenv('SCHEDULER_MAX_FAILURES', '3')),
            backoff_interval_minutes=int(os.getenv('SCHEDULER_BACKOFF', '60')),
            auto_restart=os.getenv('SCHEDULER_AUTO_RESTART', 'true').lower() == 'true'
        )

@dataclass
class ValidationConfig:
    """Data validation configuration"""
    min_listing_count: int = 10
    min_confidence_count: int = 5
    max_outlier_sigma: float = 4.0
    chaos_min: float = 0.5
    chaos_max: float = 100000.0
    
    @classmethod
    def from_env(cls) -> 'ValidationConfig':
        """Create validation config from environment variables"""
        return cls(
            min_listing_count=int(os.getenv('VALIDATION_MIN_LISTING_COUNT', '10')),
            min_confidence_count=int(os.getenv('VALIDATION_MIN_CONFIDENCE_COUNT', '5')),
            max_outlier_sigma=float(os.getenv('VALIDATION_MAX_OUTLIER_SIGMA', '4.0')),
            chaos_min=float(os.getenv('VALIDATION_CHAOS_MIN', '0.5')),
            chaos_max=float(os.getenv('VALIDATION_CHAOS_MAX', '100000.0'))
        )

@dataclass
class LoggingConfig:
    """Logging configuration"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: Optional[str] = None
    max_file_size_mb: int = 100
    backup_count: int = 5
    
    @classmethod
    def from_env(cls) -> 'LoggingConfig':
        """Create logging config from environment variables"""
        return cls(
            level=os.getenv('LOG_LEVEL', 'INFO'),
            format=os.getenv('LOG_FORMAT', cls.format),
            file_path=os.getenv('LOG_FILE_PATH'),
            max_file_size_mb=int(os.getenv('LOG_MAX_FILE_SIZE_MB', '100')),
            backup_count=int(os.getenv('LOG_BACKUP_COUNT', '5'))
        )

@dataclass
class WebConfig:
    """Web server configuration"""
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = False
    cors_origins: list = None
    
    def __post_init__(self):
        if self.cors_origins is None:
            self.cors_origins = ["http://localhost:3000", "http://localhost:8080"]
    
    @classmethod
    def from_env(cls) -> 'WebConfig':
        """Create web config from environment variables"""
        cors_origins = os.getenv('CORS_ORIGINS', 'http://localhost:3000,http://localhost:8080')
        
        return cls(
            host=os.getenv('WEB_HOST', '0.0.0.0'),
            port=int(os.getenv('WEB_PORT', '8000')),
            debug=os.getenv('DEBUG', 'false').lower() == 'true',
            cors_origins=cors_origins.split(',') if cors_origins else []
        )

class Settings:
    """Main settings class that combines all configuration"""
    
    def __init__(self):
        self.database = DatabaseConfig.from_env()
        self.api = APIConfig.from_env()
        self.scheduler = SchedulerConfig.from_env()
        self.validation = ValidationConfig.from_env()
        self.logging = LoggingConfig.from_env()
        self.web = WebConfig.from_env()
        
        # Derived settings
        self.project_root = Path(__file__).parent.parent
        self.data_dir = self.project_root / "data"
        self.logs_dir = self.project_root / "logs"
        
        # Ensure directories exist
        self.data_dir.mkdir(exist_ok=True)
        self.logs_dir.mkdir(exist_ok=True)
        
    def get_database_url(self) -> str:
        """Get database URL"""
        return self.database.url
        
    def get_log_file_path(self) -> Optional[str]:
        """Get log file path"""
        if self.logging.file_path:
            return self.logging.file_path
        return str(self.logs_dir / "poe_dashboard.log")
        
    def is_development(self) -> bool:
        """Check if running in development mode"""
        return os.getenv('ENVIRONMENT', 'development').lower() == 'development'
        
    def is_production(self) -> bool:
        """Check if running in production mode"""
        return os.getenv('ENVIRONMENT', 'development').lower() == 'production'
        
    def to_dict(self) -> dict:
        """Convert settings to dictionary for serialization"""
        return {
            'database': {
                'min_connections': self.database.min_connections,
                'max_connections': self.database.max_connections
            },
            'api': {
                'base_url': self.api.base_url,
                'default_league': self.api.default_league,
                'max_concurrent_requests': self.api.max_concurrent_requests,
                'request_timeout_seconds': self.api.request_timeout_seconds,
                'max_requests_per_minute': self.api.max_requests_per_minute,
                'max_endpoint_requests_per_minute': self.api.max_endpoint_requests_per_minute
            },
            'scheduler': {
                'poll_interval_minutes': self.scheduler.poll_interval_minutes,
                'offset_minutes': self.scheduler.offset_minutes,
                'jitter_seconds': self.scheduler.jitter_seconds,
                'max_consecutive_failures': self.scheduler.max_consecutive_failures,
                'backoff_interval_minutes': self.scheduler.backoff_interval_minutes,
                'auto_restart': self.scheduler.auto_restart
            },
            'validation': {
                'min_listing_count': self.validation.min_listing_count,
                'min_confidence_count': self.validation.min_confidence_count,
                'max_outlier_sigma': self.validation.max_outlier_sigma,
                'chaos_min': self.validation.chaos_min,
                'chaos_max': self.validation.chaos_max
            },
            'web': {
                'host': self.web.host,
                'port': self.web.port,
                'debug': self.web.debug,
                'cors_origins': self.web.cors_origins
            },
            'environment': {
                'is_development': self.is_development(),
                'is_production': self.is_production(),
                'project_root': str(self.project_root),
                'data_dir': str(self.data_dir),
                'logs_dir': str(self.logs_dir)
            }
        }

# Global settings instance
settings = Settings()

def get_settings() -> Settings:
    """Get the global settings instance"""
    return settings
