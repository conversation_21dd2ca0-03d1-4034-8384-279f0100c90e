import React, { useEffect, useState } from 'react';
import { useAppStore } from '../../stores/appStore';
import { 
  getTopFlipOpportunities, 
  getPortfolioTotalValue, 
  getMarketSummary 
} from '../../utils/tauri';
import type { FlipOpportunity, MarketSummary } from '../../types';
import QuickPriceCard from './QuickPriceCard';
import FlipFinderCard from './FlipFinderCard';
import PortfolioCard from './PortfolioCard';
import MarketSummaryCard from './MarketSummaryCard';
import { GeminiChat } from '../Chat/GeminiChat';


interface DashboardData {
  topFlips: FlipOpportunity[];
  portfolioValue: number;
  marketSummary: MarketSummary;
}

const Dashboard: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [isChatOpen, setChatOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | undefined>();
  const { currentLeague } = useAppStore();

  useEffect(() => {
    loadDashboardData();
    
    // Refresh every 5 minutes
    const interval = setInterval(loadDashboardData, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, [currentLeague]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(undefined);
      
      // Load data in parallel for better performance
      const [topFlips, portfolioValue, marketSummary] = await Promise.all([
        getTopFlipOpportunities(currentLeague, 5),
        getPortfolioTotalValue(currentLeague),
        getMarketSummary(currentLeague),
      ]);

      console.debug('Market Summary:', marketSummary);
      setDashboardData({
        topFlips,
        portfolioValue,
        marketSummary,
      });
    } catch (err) {
      console.error('Failed to load dashboard data:', err);
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="card p-6">
              <div className="animate-pulse space-y-4">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="card p-6 text-center">
          <div className="text-red-600 dark:text-red-400 text-4xl mb-4">⚠️</div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
            Dashboard Error
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
          <button
            onClick={loadDashboardData}
            className="btn-primary"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6 animate-fade-in">
      {/* Welcome Section */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          Welcome to PoE Profit AI
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Your lightning-fast companion for Path of Exile market analysis
        </p>
      </div>

      {/* Main Dashboard Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {/* Quick Price - Always visible */}
        <div className="xl:col-span-1">
          <QuickPriceCard />
        </div>
        
        {/* Top Flips */}
        <div className="xl:col-span-2">
          <FlipFinderCard 
            opportunities={dashboardData?.topFlips || []}
            onViewAll={() => useAppStore.getState().setCurrentTab('flip-finder')}
          />
        </div>
        
        {/* Portfolio Overview */}
        <div className="lg:col-span-1">
          <PortfolioCard 
            totalValue={dashboardData?.portfolioValue || 0}
            onViewAll={() => useAppStore.getState().setCurrentTab('portfolio')}
          />
        </div>
        
        {/* Market Summary */}
        <div className="lg:col-span-1 xl:col-span-2">
          <MarketSummaryCard 
            marketSummary={dashboardData?.marketSummary}
          />
        </div>
      </div>

      {/* Quick Actions */}
      {isChatOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg w-full max-w-2xl h-3/4">
            <GeminiChat />
            <button
              onClick={() => setChatOpen(false)}
              className="btn-secondary mt-4"
            >
              Close
            </button>
          </div>
        </div>
      )}

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
        <button
          onClick={() => setChatOpen(true)}
          className="card p-4 hover:shadow-lg transition-shadow text-center"
        >
          <div className="text-2xl mb-2">🤖</div>
          <div className="font-medium text-gray-900 dark:text-gray-100">
            AI Chat
          </div>
        </button>
        <button
          onClick={() => useAppStore.getState().setCurrentTab('quick-price')}
          className="card p-4 hover:shadow-lg transition-shadow text-center"
        >
          <div className="text-2xl mb-2">🔍</div>
          <div className="font-medium text-gray-900 dark:text-gray-100">
            Price Check
          </div>
        </button>
        
        <button
          onClick={() => useAppStore.getState().setCurrentTab('flip-finder')}
          className="card p-4 hover:shadow-lg transition-shadow text-center"
        >
          <div className="text-2xl mb-2">📈</div>
          <div className="font-medium text-gray-900 dark:text-gray-100">
            Find Flips
          </div>
        </button>
        
        <button
          onClick={() => useAppStore.getState().setCurrentTab('portfolio')}
          className="card p-4 hover:shadow-lg transition-shadow text-center"
        >
          <div className="text-2xl mb-2">💼</div>
          <div className="font-medium text-gray-900 dark:text-gray-100">
            Portfolio
          </div>
        </button>
        
        <button
          onClick={() => useAppStore.getState().setCurrentTab('league-lab')}
          className="card p-4 hover:shadow-lg transition-shadow text-center"
        >
          <div className="text-2xl mb-2">🧪</div>
          <div className="font-medium text-gray-900 dark:text-gray-100">
            League Lab
          </div>
        </button>
      </div>
    </div>
  );
};

export default Dashboard;
