import React, { useState, useEffect } from 'react';
import { TrendingUp, Filter, RefreshCw, AlertCircle } from 'lucide-react';
import { useAppStore } from '../../stores/appStore';
import { findFlipOpportunities } from '../../utils/tauri';
import type { FlipFinderQuery, FlipOpportunity, RiskLevel } from '../../types';
import FlipOpportunityCard from './FlipOpportunityCard';
import FlipFinderFilters from './FlipFinderFilters';
import LoadingSpinner from '../UI/LoadingSpinner';

const FlipFinder: React.FC = () => {
  const [opportunities, setOpportunities] = useState<FlipOpportunity[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | undefined>();
  const [showFilters, setShowFilters] = useState(false);
  const { currentLeague } = useAppStore();

  const [filters, setFilters] = useState<FlipFinderQuery>({
    budget_chaos: 1000,
    min_profit_pct: 10,
    max_risk_level: 'Medium' as RiskLevel,
    league: currentLeague,
  });

  useEffect(() => {
    setFilters(prev => ({ ...prev, league: currentLeague }));
  }, [currentLeague]);

  useEffect(() => {
    // Auto-search on component mount
    handleSearch();
  }, []);

  const handleSearch = async () => {
    setLoading(true);
    setError(undefined);
    
    try {
      const results = await findFlipOpportunities(filters);
      setOpportunities(results);
    } catch (err) {
      console.error('Failed to find flip opportunities:', err);
      setError('Failed to find flip opportunities. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleFiltersChange = (newFilters: Partial<FlipFinderQuery>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const totalPotentialProfit = opportunities.reduce(
    (sum, opp) => sum + opp.profit_chaos, 
    0
  );

  const averageROI = opportunities.length > 0 
    ? opportunities.reduce((sum, opp) => sum + opp.profit_pct, 0) / opportunities.length
    : 0;

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            Flip Finder
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Discover profitable arbitrage opportunities in the {currentLeague} league
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`btn-secondary flex items-center ${showFilters ? 'bg-poe-accent text-white' : ''}`}
          >
            <Filter className="w-4 h-4 mr-2" />
            Filters
          </button>
          
          <button
            onClick={handleSearch}
            disabled={loading}
            className="btn-primary flex items-center"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            {loading ? 'Searching...' : 'Refresh'}
          </button>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="card p-6 animate-slide-up">
          <FlipFinderFilters
            filters={filters}
            onChange={handleFiltersChange}
            onSearch={handleSearch}
            loading={loading}
          />
        </div>
      )}

      {/* Stats Summary */}
      {opportunities.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="card p-4 text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">Opportunities Found</p>
            <p className="text-2xl font-bold text-poe-accent">{opportunities.length}</p>
          </div>
          
          <div className="card p-4 text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">Total Potential Profit</p>
            <p className="text-2xl font-bold chaos-value">
              {totalPotentialProfit.toFixed(0)}c
            </p>
          </div>
          
          <div className="card p-4 text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">Average ROI</p>
            <p className="text-2xl font-bold profit-positive">
              {averageROI.toFixed(1)}%
            </p>
          </div>
          
          <div className="card p-4 text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">Budget Used</p>
            <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {((opportunities.reduce((sum, opp) => sum + opp.buy_price, 0) / filters.budget_chaos) * 100).toFixed(0)}%
            </p>
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="card p-12 text-center">
          <LoadingSpinner size="large" className="mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400">
            Analyzing market data for profitable opportunities...
          </p>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="card p-6 text-center">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
            Search Failed
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
          <button onClick={handleSearch} className="btn-primary">
            Try Again
          </button>
        </div>
      )}

      {/* No Results */}
      {!loading && !error && opportunities.length === 0 && (
        <div className="card p-12 text-center">
          <TrendingUp className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
            No Opportunities Found
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Try adjusting your search criteria:
          </p>
          <ul className="text-sm text-gray-500 dark:text-gray-400 space-y-1">
            <li>• Increase your budget</li>
            <li>• Lower the minimum profit percentage</li>
            <li>• Allow higher risk investments</li>
            <li>• Check back later for new opportunities</li>
          </ul>
        </div>
      )}

      {/* Results */}
      {!loading && opportunities.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              Flip Opportunities ({opportunities.length})
            </h2>
            
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Sorted by profit percentage
            </div>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4" data-testid="flip-opportunities">
            {opportunities.map((opportunity, index) => (
              <FlipOpportunityCard
                key={index}
                opportunity={opportunity}
                rank={index + 1}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default FlipFinder;
