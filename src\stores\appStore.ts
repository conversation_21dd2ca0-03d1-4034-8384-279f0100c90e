import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { AppState, NavigationTab, League } from '../types';

export type SyncState = 'idle' | 'running' | 'completed' | 'error-timeout' | 'pending' | 'error';

export interface SyncSlice {
  syncState: SyncState;
  syncProgress: number; // 0-100
  lastBeat: number; // Date.now()
  lastSync?: Date;
  errorMsg?: string;
  updateProgress: (pct: number, ts: number) => void;
}

interface AppStore extends AppState, SyncSlice {
  // Navigation
  currentTab: NavigationTab;
  setCurrentTab: (tab: NavigationTab) => void;
  
  // League management
  leagues: League[];
  setLeagues: (leagues: League[]) => void;
  setCurrentLeague: (league: string) => void;
  
  // App state
  setTheme: (theme: 'light' | 'dark') => void;
  setLoading: (loading: boolean) => void;
  setError: (error?: string) => void;
  
  // Sync status
  setSyncStatus: (status: SyncState, meta?: Partial<Omit<SyncSlice, 'syncState'>>) => void;
  
  // Performance metrics
  lastQueryTime: number;
  setLastQueryTime: (time: number) => void;
}

export const useAppStore = create<AppStore>()(
  persist(
    (set) => ({
      // Initial state
      currentLeague: 'Standard',
      theme: 'dark',
      isLoading: false,
      error: undefined,
      currentTab: 'dashboard',
      leagues: [],
      syncState: 'idle',
      syncProgress: 0,
      lastBeat: 0,
      lastSync: undefined,
      errorMsg: undefined,
      lastQueryTime: 0,

      // Actions
      setCurrentTab: (tab) => set({ currentTab: tab }),

      updateProgress: (pct, ts) => set({ syncProgress: pct, lastBeat: ts }),

      setLeagues: (leagues) => set({ leagues }),
      
      setCurrentLeague: (league) => {
        set({ currentLeague: league });
        // You could also call the Tauri command here to persist the change
      },
      
      setTheme: (theme) => {
        set({ theme });
        // Update document class for Tailwind dark mode
        if (theme === 'dark') {
          document.documentElement.classList.add('dark');
        } else {
          document.documentElement.classList.remove('dark');
        }
      },
      
      setLoading: (isLoading) => set({ isLoading }),
      
      setError: (error) => set({ error }),
      
      setSyncStatus: (status, meta) => set({ syncState: status, ...meta }),
      
      setLastQueryTime: (lastQueryTime) => set({ lastQueryTime }),
    }),
    {
      name: 'poe-profit-ai-store',
      partialize: (state) => ({
        currentLeague: state.currentLeague,
        theme: state.theme,
        currentTab: state.currentTab,
      }),
    }
  )
);

// Initialize theme on app start
const initialTheme = useAppStore.getState().theme;
if (initialTheme === 'dark') {
  document.documentElement.classList.add('dark');
} else {
  document.documentElement.classList.remove('dark');
}
