use sqlx::{PgPool, Row};
use anyhow::Result;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

use crate::{FlipOpportunity, League, MarketSummary, TopMover, CurrencyRates};

#[derive(Debug, Clone)]
pub struct DatabaseManager {
    pool: PgPool,
}

impl DatabaseManager {
    pub async fn new(database_url: &str) -> Result<Self> {
        let pool = PgPool::connect(database_url).await?;
        
        // Run migrations if needed
        sqlx::migrate!("./migrations").run(&pool).await?;
        
        Ok(Self { pool })
    }

    pub fn get_pool(&self) -> &PgPool {
        &self.pool
    }

    // Get top flip opportunities from real database
    pub async fn get_top_flip_opportunities(&self, league: &str, limit: usize) -> Result<Vec<FlipOpportunity>> {
        let query = r#"
            SELECT
                i.name as item_name,
                p.chaos_value as current_price,
                p.pay_value,
                p.receive_value,
                p.listing_count,
                p.confidence_score,
                p.liquidity_score,
                p.volatility,
                CASE
                    WHEN p.volatility < 0.1 THEN 'Low'
                    WHEN p.volatility < 0.3 THEN 'Medium'
                    ELSE 'High'
                END as risk_level
            FROM prices p
            JOIN items i ON p.item_id = i.id
            JOIN leagues l ON p.league_id = l.id
            WHERE l.name = $1
                AND p.pay_value IS NOT NULL
                AND p.receive_value IS NOT NULL
                AND p.confidence_score > 0.7
                AND p.listing_count > 5
            ORDER BY
                (p.receive_value - p.pay_value) / p.pay_value DESC,
                p.confidence_score DESC
            LIMIT $2
        "#;

        let rows = sqlx::query(query)
            .bind(league)
            .bind(limit as i64)
            .fetch_all(&self.pool)
            .await?;

        let opportunities = rows.into_iter().map(|row| {
            let pay_value: f64 = row.try_get("pay_value").unwrap_or_default();
            let receive_value: f64 = row.try_get("receive_value").unwrap_or_default();
            let profit = receive_value - pay_value;
            let profit_pct = if pay_value > 0.0 { (profit / pay_value) * 100.0 } else { 0.0 };

            FlipOpportunity {
                item_name: row.try_get("item_name").unwrap_or_default(),
                buy_price: pay_value,
                sell_price: receive_value,
                profit_chaos: profit,
                profit_pct,
                risk_level: row.try_get("risk_level").unwrap_or_else(|_| "Medium".to_string()),
                confidence: row.try_get::<f64, _>("confidence_score").unwrap_or_default(),
                liquidity_score: row.try_get::<f64, _>("liquidity_score").unwrap_or_default(),
                time_to_sell_estimate: estimate_sell_time(row.try_get::<f64, _>("liquidity_score").unwrap_or_default()),
                listing_count: row.try_get::<i32, _>("listing_count").unwrap_or_default(),
            }
        }).collect();

        Ok(opportunities)
    }

    // Get portfolio total value
    pub async fn get_portfolio_total_value(&self, league: &str) -> Result<f64> {
        let query = r#"
            SELECT COALESCE(SUM(
                CASE
                    WHEN p.chaos_value IS NOT NULL THEN portfolio.quantity * p.chaos_value
                    ELSE 0
                END
            ), 0) as total_value
            FROM portfolio
            LEFT JOIN items i ON portfolio.item_name = i.name
            LEFT JOIN leagues l ON portfolio.league = l.name
            LEFT JOIN prices p ON i.id = p.item_id AND l.id = p.league_id
            WHERE portfolio.league = $1
        "#;

        let row = sqlx::query(query)
            .bind(league)
            .fetch_one(&self.pool)
            .await?;

        Ok(row.try_get::<f64, _>("total_value").unwrap_or_default())
    }

    // Get market summary
    pub async fn get_market_summary(&self, league: &str) -> Result<MarketSummary> {
        // Get total items count
        let total_items_query = "SELECT COUNT(DISTINCT i.id) as count FROM items i JOIN prices p ON i.id = p.item_id JOIN leagues l ON p.league_id = l.id WHERE l.name = $1";
        let total_items_row = sqlx::query(total_items_query)
            .bind(league)
            .fetch_one(&self.pool)
            .await?;

        // Get active leagues count
        let active_leagues_query = "SELECT COUNT(*) as count FROM leagues WHERE is_active = true";
        let active_leagues_row = sqlx::query(active_leagues_query)
            .fetch_one(&self.pool)
            .await?;

        // Get top movers (items with highest price changes)
        let top_movers_query = r#"
            SELECT
                i.name as item_name,
                p.chaos_value as current_price,
                p.momentum_7d as change_pct,
                p.listing_count as volume
            FROM prices p
            JOIN items i ON p.item_id = i.id
            JOIN leagues l ON p.league_id = l.id
            WHERE l.name = $1
                AND p.momentum_7d IS NOT NULL
                AND p.chaos_value IS NOT NULL
            ORDER BY ABS(p.momentum_7d) DESC
            LIMIT 5
        "#;
        let top_movers_rows = sqlx::query(top_movers_query)
            .bind(league)
            .fetch_all(&self.pool)
            .await?;

        let top_movers = top_movers_rows.into_iter().map(|row| TopMover {
            item_name: row.try_get("item_name").unwrap_or_default(),
            change_pct: row.try_get::<f64, _>("change_pct").unwrap_or_default(),
            current_price: row.try_get::<f64, _>("current_price").unwrap_or_default(),
            volume: row.try_get::<i64, _>("volume").unwrap_or_default() as u64,
        }).collect();

        // Get currency rates (Divine Orb price in chaos)
        let divine_price_query = r#"
            SELECT p.chaos_value
            FROM prices p
            JOIN items i ON p.item_id = i.id
            JOIN leagues l ON p.league_id = l.id
            WHERE l.name = $1 AND i.name = 'Divine Orb'
            ORDER BY p.snapshot_time DESC
            LIMIT 1
        "#;
        let divine_price_row = sqlx::query(divine_price_query)
            .bind(league)
            .fetch_optional(&self.pool)
            .await?;

        let divine_to_chaos = divine_price_row
            .and_then(|row| row.try_get::<f64, _>("chaos_value").ok())
            .unwrap_or(220.0);

        Ok(MarketSummary {
            total_items: total_items_row.try_get::<i64, _>("count").unwrap_or_default() as u64,
            active_leagues: active_leagues_row.try_get::<i64, _>("count").unwrap_or_default() as u64,
            last_update: Utc::now().to_rfc3339(),
            top_movers,
            currency_rates: CurrencyRates {
                chaos_to_divine: 1.0 / divine_to_chaos,
                divine_to_chaos,
                exalt_to_chaos: Some(16.5), // Could be fetched from DB too
            },
        })
    }

    // Get leagues list
    pub async fn get_leagues(&self) -> Result<Vec<League>> {
        let query = "SELECT id, name, display_name, is_active, first_seen, last_seen FROM leagues ORDER BY is_active DESC, name";
        let rows = sqlx::query(query)
            .fetch_all(&self.pool)
            .await?;

        let leagues = rows.into_iter().map(|row| League {
            id: row.try_get("id").unwrap_or_default(),
            name: row.try_get("name").unwrap_or_default(),
            display_name: row.try_get("display_name").unwrap_or_default(),
            is_active: row.try_get("is_active").unwrap_or_default(),
            first_seen: row.try_get::<DateTime<Utc>, _>("first_seen").unwrap_or_else(|_| Utc::now()).to_rfc3339(),
            last_seen: row.try_get::<DateTime<Utc>, _>("last_seen").unwrap_or_else(|_| Utc::now()).to_rfc3339(),
        }).collect();

        Ok(leagues)
    }

    // Get sync status from database
    pub async fn get_sync_status(&self) -> Result<crate::SyncStatus> {
        let query = r#"
            SELECT
                COUNT(*) as items_synced,
                MAX(fetch_time) as last_sync,
                AVG(latency_ms) as avg_latency
            FROM stats_fetch
            WHERE fetch_time > NOW() - INTERVAL '1 hour'
        "#;
        let row = sqlx::query(query)
            .fetch_one(&self.pool)
            .await?;

        // Check if there's an active sync process
        let is_syncing = false; // This would be determined by checking a sync lock or status table

        Ok(crate::SyncStatus {
            is_syncing,
            last_sync: row.try_get::<DateTime<Utc>, _>("last_sync").ok().map(|dt| dt.to_rfc3339()),
            last_error: None, // Could be fetched from error logs
            items_synced: row.try_get::<i64, _>("items_synced").unwrap_or_default() as u64,
            sync_duration_ms: row.try_get::<f64, _>("avg_latency").unwrap_or_default() as u64,
            next_sync_eta: Some(
                (Utc::now() + chrono::Duration::minutes(31)).to_rfc3339()
            ),
        })
    }
}

fn estimate_sell_time(liquidity_score: f64) -> String {
    match liquidity_score {
        x if x > 0.8 => "< 1 hour".to_string(),
        x if x > 0.5 => "1-3 hours".to_string(),
        x if x > 0.2 => "3-12 hours".to_string(),
        _ => "12+ hours".to_string(),
    }
}
