import React from 'react';
import { BarChart3, TrendingUp, TrendingDown } from 'lucide-react';
import type { MarketSummary } from '../../types';
import { formatCurrency, formatTimeAgo } from '../../utils/tauri';

interface MarketSummaryCardProps {
  marketSummary?: MarketSummary;
}

const MarketSummaryCard: React.FC<MarketSummaryCardProps> = ({ marketSummary }) => {
  if (!marketSummary) {
    return (
      <div className="card p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="card p-6">
      <h2 className="text-xl font-semibold mb-4 flex items-center text-gray-900 dark:text-gray-100">
        <BarChart3 className="w-5 h-5 mr-2" />
        Market Summary
      </h2>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div>
          <p className="text-sm text-gray-600 dark:text-gray-400">Total Items</p>
          <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {marketSummary.total_items.toLocaleString()}
          </p>
        </div>
        
        <div>
          <p className="text-sm text-gray-600 dark:text-gray-400">Active Leagues</p>
          <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {marketSummary.active_leagues}
          </p>
        </div>
        
        <div>
          <p className="text-sm text-gray-600 dark:text-gray-400">Chaos:Divine</p>
          <p className="text-2xl font-bold divine-value">
            1:{marketSummary.currency_rates.divine_to_chaos.toFixed(0)}
          </p>
        </div>
        
        <div>
          <p className="text-sm text-gray-600 dark:text-gray-400">Last Update</p>
          <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
            {formatTimeAgo(marketSummary.last_update)}
          </p>
        </div>
      </div>

      {/* Top Movers */}
      <div>
        <h3 className="text-lg font-medium mb-3 text-gray-900 dark:text-gray-100">
          Top Movers (24h)
        </h3>
        
        <div className="space-y-2">
          {marketSummary.top_movers.map((mover, index) => (
            <div
              key={index}
              className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
            >
              <div className="flex items-center space-x-3">
                {mover.change_pct >= 0 ? (
                  <TrendingUp className="w-4 h-4 text-green-500" />
                ) : (
                  <TrendingDown className="w-4 h-4 text-red-500" />
                )}
                <span className="font-medium text-gray-900 dark:text-gray-100">
                  {mover.item_name}
                </span>
              </div>
              
              <div className="text-right">
                <div className={`font-semibold ${
                  mover.change_pct >= 0 ? 'profit-positive' : 'profit-negative'
                }`}>
                  {mover.change_pct >= 0 ? '+' : ''}{mover.change_pct.toFixed(1)}%
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {formatCurrency(mover.current_price, 'chaos')}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default MarketSummaryCard;
