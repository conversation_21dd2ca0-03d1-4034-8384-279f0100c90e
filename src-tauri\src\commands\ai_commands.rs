use serde_json::{json, Value};
use tauri::State;

use crate::{
    ai::gemini::{send_chat_message as send_gemini_chat_message, ChatMessage},
    data::query_service::{ItemFilters, PriceFilters, QueryService},
    AppState,
};

#[derive(serde::Deserialize)]
#[serde(tag = "table", rename_all = "camelCase")]
pub enum QueryPayload {
    Items(ItemFilters),
    Prices(PriceFilters),
}

#[tauri::command]
pub async fn query_database(
    payload: QueryPayload,
    state: State<'_, AppState>,
) -> Result<String, String> {
    let query_service = QueryService::new(state.db_manager.get_pool().clone());

    let result = match payload {
        QueryPayload::Items(filters) => {
            let items = query_service
                .query_items(filters)
                .await
                .map_err(|e| e.to_string())?;
            json!(items)
        }
        QueryPayload::Prices(filters) => {
            let prices = query_service
                .query_prices(filters)
                .await
                .map_err(|e| e.to_string())?;
            json!(prices)
        }
    };

    serde_json::to_string(&result).map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn send_chat_message(
    history: Vec<ChatMessage>,
    tools: Vec<Value>,
    state: State<'_, AppState>,
) -> Result<Value, String> {
    // For now, we'll use a hardcoded API key for simplicity.
    // In a real application, this should be loaded securely.
    let api_key = std::env::var("GEMINI_API_KEY").expect("GEMINI_API_KEY not set");

    send_gemini_chat_message(history, tools, &api_key)
        .await
        .map_err(|e| e.to_string())
}
