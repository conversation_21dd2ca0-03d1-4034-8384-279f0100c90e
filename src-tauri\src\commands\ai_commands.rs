// AI commands temporarily disabled - need to implement AI module
// use tauri::State;
// use crate::{AppState, models::*};

/*
#[tauri::command]
pub async fn analyze_flip_opportunities_ai(
    query: FlipFinderQuery,
    state: State<'_, AppState>,
) -> Result<Vec<FlipPrediction>, String> {
    // AI functionality temporarily disabled
    Err("AI functionality not yet implemented".to_string())
}
*/

/*
// All AI commands temporarily disabled until AI module is implemented
*/
