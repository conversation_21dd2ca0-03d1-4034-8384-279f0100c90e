#!/bin/bash

# Build script for PoE Profit AI

echo "🔨 Building PoE Profit AI for Production"

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf dist/
rm -rf src-tauri/target/release/

# Install dependencies
echo "📦 Installing dependencies..."
npm ci

# Run tests
echo "🧪 Running tests..."
npm test
if [ $? -ne 0 ]; then
    echo "❌ Tests failed. Aborting build."
    exit 1
fi

# Lint code
echo "🔍 Linting code..."
npm run lint
if [ $? -ne 0 ]; then
    echo "❌ Linting failed. Aborting build."
    exit 1
fi

# Build frontend
echo "🏗️ Building frontend..."
npm run build

# Build Tauri app
echo "🦀 Building Tauri application..."
npm run tauri:build

echo "✅ Build completed successfully!"
echo "📦 Binaries are available in src-tauri/target/release/bundle/"
