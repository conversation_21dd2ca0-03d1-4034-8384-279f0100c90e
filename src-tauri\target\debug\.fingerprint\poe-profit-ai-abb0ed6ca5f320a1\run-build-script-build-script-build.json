{"rustc": 10895048813736897673, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[11304881205945495006, "build_script_build", false, 11111443815501117479], [10755362358622467486, "build_script_build", false, 1804714126154784306]], "local": [{"RerunIfChanged": {"output": "debug\\build\\poe-profit-ai-abb0ed6ca5f320a1\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}