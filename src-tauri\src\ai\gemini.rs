use anyhow::Result;
use reqwest::Client;
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};

#[derive(Serialize, Deserialize, Debug)]
pub struct ChatMessage {
    role: String,
    parts: Vec<Part>,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct Part {
    #[serde(skip_serializing_if = "Option::is_none")]
    text: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    tool_call: Option<ToolCall>,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct ToolCall {
    name: String,
    args: Value,
}

#[derive(Deserialize, Debug)]
struct GeminiResponse {
    candidates: Vec<Candidate>,
}

#[derive(Deserialize, Debug)]
struct Candidate {
    content: Content,
}

#[derive(Deserialize, Debug)]
struct Content {
    parts: Vec<PartResponse>,
}

#[derive(Deserialize, Debug)]
struct PartResponse {
    text: Option<String>,
    tool_call: Option<FunctionCall>,
}

#[derive(Deserialize, Debug, Serialize)]
pub struct FunctionCall {
    pub name: String,
    pub args: Value,
}

#[derive(Serialize)]
struct GeminiRequest<'a> {
    contents: &'a [ChatMessage],
    tools: &'a [Value],
}

pub async fn send_chat_message(
    history: Vec<ChatMessage>,
    tools: Vec<Value>,
    api_key: &str,
) -> Result<Value> {
    let url = format!(
        "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key={}",
        api_key
    );

    let body = GeminiRequest {
        contents: &history,
        tools: &tools,
    };

    let client = Client::new();
    let response = client.post(&url).json(&body).send().await?;

    let status = response.status();
    let response_text = response.text().await?;

    if !status.is_success() {
        log::error!("Gemini API Error: status={}, body={}", status, &response_text);
        return Err(anyhow::anyhow!(
            "Gemini API request failed with status {}: {}",
            status,
            response_text
        ));
    }

    log::debug!("Gemini API Response: {}", &response_text);

    let resp: GeminiResponse = match serde_json::from_str(&response_text) {
        Ok(json) => json,
        Err(e) => {
            log::error!("Failed to parse Gemini JSON response: {}", e);
            log::error!("Raw response was: {}", &response_text);
            return Err(anyhow::anyhow!("Failed to decode Gemini response: {}", e));
        }
    };

    if let Some(candidate) = resp.candidates.first() {
        if let Some(part) = candidate.content.parts.first() {
            if let Some(tool_call) = &part.tool_call {
                return Ok(json!({ "tool_calls": [ { "function_call": tool_call } ] }));
            }
            if let Some(text) = &part.text {
                return Ok(json!({ "text": text }));
            }
        }
    }

    Ok(json!({ "text": "No meaningful response from Gemini." }))
}