use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use anyhow::Result;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct AppConfig {
    pub database_url: String,
    pub sync_interval_minutes: u64,
    pub default_league: String,
    pub api_rate_limit: u32,
    pub log_level: String,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            database_url: std::env::var("DATABASE_URL")
                .unwrap_or_else(|_| "postgresql://localhost/poe_dashboard".to_string()),
            sync_interval_minutes: 31, // Your preferred 31-minute cadence
            default_league: "Standard".to_string(),
            api_rate_limit: 45, // requests per minute
            log_level: "info".to_string(),
        }
    }
}

impl AppConfig {
    pub async fn load() -> Result<Self> {
        // Try to load from environment variables first
        let config = Self {
            database_url: std::env::var("DATABASE_URL")
                .unwrap_or_else(|_| Self::default().database_url),
            sync_interval_minutes: std::env::var("SYNC_INTERVAL_MINUTES")
                .ok()
                .and_then(|s| s.parse().ok())
                .unwrap_or(31),
            default_league: std::env::var("DEFAULT_LEAGUE")
                .unwrap_or_else(|_| "Standard".to_string()),
            api_rate_limit: std::env::var("API_RATE_LIMIT")
                .ok()
                .and_then(|s| s.parse().ok())
                .unwrap_or(45),
            log_level: std::env::var("LOG_LEVEL")
                .unwrap_or_else(|_| "info".to_string()),
        };

        Ok(config)
    }

    pub fn get_config_dir() -> Result<PathBuf> {
        let config_dir = dirs::config_dir()
            .ok_or_else(|| anyhow::anyhow!("Could not find config directory"))?
            .join("poe-profit-ai");
        
        std::fs::create_dir_all(&config_dir)?;
        Ok(config_dir)
    }
}
