#!/usr/bin/env python3
"""
Simple script to create placeholder icons for Tauri app
"""

from PIL import Image, ImageDraw
import os

def create_icon(size, filename):
    """Create a simple colored square icon"""
    # Create a new image with a dark blue background
    img = Image.new('RGBA', (size, size), (30, 58, 138, 255))  # Dark blue
    draw = ImageDraw.Draw(img)
    
    # Add a simple design - a golden border and "P" for PoE
    border_width = max(1, size // 16)
    
    # Golden border
    draw.rectangle([0, 0, size-1, size-1], outline=(255, 215, 0, 255), width=border_width)
    
    # Add a simple "P" in the center
    if size >= 32:
        font_size = size // 3
        # Simple P shape using rectangles
        p_width = font_size // 3
        p_height = font_size
        x_start = (size - p_width) // 2
        y_start = (size - p_height) // 2
        
        # Vertical line of P
        draw.rectangle([x_start, y_start, x_start + p_width//3, y_start + p_height], 
                      fill=(255, 215, 0, 255))
        
        # Top horizontal line of P
        draw.rectangle([x_start, y_start, x_start + p_width, y_start + p_width//3], 
                      fill=(255, 215, 0, 255))
        
        # Middle horizontal line of P
        draw.rectangle([x_start, y_start + p_height//3, x_start + p_width*2//3, y_start + p_height//3 + p_width//3], 
                      fill=(255, 215, 0, 255))
    
    img.save(filename)
    print(f"Created {filename} ({size}x{size})")

def main():
    # Create icons directory if it doesn't exist
    icons_dir = "src-tauri/icons"
    os.makedirs(icons_dir, exist_ok=True)
    
    # Create required icon sizes
    create_icon(32, os.path.join(icons_dir, "32x32.png"))
    create_icon(128, os.path.join(icons_dir, "128x128.png"))
    create_icon(256, os.path.join(icons_dir, "<EMAIL>"))  # 2x version
    
    # Create ICO file (Windows)
    img = Image.new('RGBA', (32, 32), (30, 58, 138, 255))
    draw = ImageDraw.Draw(img)
    draw.rectangle([0, 0, 31, 31], outline=(255, 215, 0, 255), width=2)
    img.save(os.path.join(icons_dir, "icon.ico"))
    print("Created icon.ico")
    
    # Create ICNS file (macOS) - simplified version
    img.save(os.path.join(icons_dir, "icon.icns"))
    print("Created icon.icns")
    
    print("All icons created successfully!")

if __name__ == "__main__":
    main()
