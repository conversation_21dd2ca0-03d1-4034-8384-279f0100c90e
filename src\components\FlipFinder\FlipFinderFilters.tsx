import React from 'react';
import { Search } from 'lucide-react';
import type { FlipFinderQuery, RiskLevel } from '../../types';

interface FlipFinderFiltersProps {
  filters: FlipFinderQuery;
  onChange: (filters: Partial<FlipFinderQuery>) => void;
  onSearch: () => void;
  loading: boolean;
}

const FlipFinderFilters: React.FC<FlipFinderFiltersProps> = ({
  filters,
  onChange,
  onSearch,
  loading,
}) => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch();
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Budget */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Budget (Chaos Orbs)
          </label>
          <input
            type="number"
            min="1"
            max="100000"
            value={filters.budget_chaos}
            onChange={(e) => onChange({ budget_chaos: Number(e.target.value) })}
            className="input-field"
            placeholder="1000"
          />
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Maximum amount you want to invest
          </p>
        </div>

        {/* Minimum Profit */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Minimum Profit (%)
          </label>
          <input
            type="number"
            min="1"
            max="1000"
            step="0.1"
            value={filters.min_profit_pct}
            onChange={(e) => onChange({ min_profit_pct: Number(e.target.value) })}
            className="input-field"
            placeholder="10"
          />
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Minimum expected return percentage
          </p>
        </div>

        {/* Risk Level */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Maximum Risk Level
          </label>
          <select
            value={filters.max_risk_level}
            onChange={(e) => onChange({ max_risk_level: e.target.value as RiskLevel })}
            className="input-field"
          >
            <option value="Low">Low Risk</option>
            <option value="Medium">Medium Risk</option>
            <option value="High">High Risk</option>
          </select>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Higher risk may yield higher returns
          </p>
        </div>
      </div>

      {/* Quick Presets */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          Quick Presets
        </label>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          <button
            type="button"
            onClick={() => onChange({ 
              budget_chaos: 100, 
              min_profit_pct: 15, 
              max_risk_level: 'Low' as RiskLevel 
            })}
            className="btn-secondary text-sm py-2"
          >
            Conservative
            <div className="text-xs opacity-75">100c, 15%, Low Risk</div>
          </button>
          
          <button
            type="button"
            onClick={() => onChange({ 
              budget_chaos: 500, 
              min_profit_pct: 10, 
              max_risk_level: 'Medium' as RiskLevel 
            })}
            className="btn-secondary text-sm py-2"
          >
            Balanced
            <div className="text-xs opacity-75">500c, 10%, Med Risk</div>
          </button>
          
          <button
            type="button"
            onClick={() => onChange({ 
              budget_chaos: 1000, 
              min_profit_pct: 8, 
              max_risk_level: 'Medium' as RiskLevel 
            })}
            className="btn-secondary text-sm py-2"
          >
            Aggressive
            <div className="text-xs opacity-75">1000c, 8%, Med Risk</div>
          </button>
          
          <button
            type="button"
            onClick={() => onChange({ 
              budget_chaos: 2000, 
              min_profit_pct: 5, 
              max_risk_level: 'High' as RiskLevel 
            })}
            className="btn-secondary text-sm py-2"
          >
            High Roller
            <div className="text-xs opacity-75">2000c, 5%, High Risk</div>
          </button>
        </div>
      </div>

      {/* Search Button */}
      <div className="flex justify-end">
        <button
          type="submit"
          disabled={loading}
          className="btn-primary flex items-center px-8"
        >
          <Search className="w-4 h-4 mr-2" />
          {loading ? 'Searching...' : 'Find Opportunities'}
        </button>
      </div>
    </form>
  );
};

export default FlipFinderFilters;
