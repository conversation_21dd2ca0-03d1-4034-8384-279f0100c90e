#!/usr/bin/env python3
"""
Basic functionality test for PoE Dashboard
==========================================
Tests the core components without requiring full database setup
"""

import asyncio
import logging
import sys
from datetime import datetime

# Setup basic logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_api_client():
    """Test the basic API client functionality"""
    logger.info("Testing PoE Ninja API client...")
    
    try:
        from poe_ninja_api import PoeNinjaAPI, EndpointType
        
        async with PoeNinjaAPI(league="Mercenaries") as api:
            # Test currency data fetch
            logger.info("Fetching currency data...")
            currency_data = await api.get_currency_data()
            
            if currency_data and 'lines' in currency_data:
                logger.info(f"✓ Currency data: {len(currency_data['lines'])} items")
                
                # Show a sample currency
                if currency_data['lines']:
                    sample = currency_data['lines'][0]
                    logger.info(f"  Sample: {sample.get('currencyTypeName')} = {sample.get('chaosEquivalent')} chaos")
            else:
                logger.error("✗ Failed to fetch currency data")
                return False
                
            # Test item data fetch
            logger.info("Fetching unique weapon data...")
            weapon_data = await api.get_items_data(EndpointType.UNIQUE_WEAPON)
            
            if weapon_data and 'lines' in weapon_data:
                logger.info(f"✓ Unique weapon data: {len(weapon_data['lines'])} items")
                
                # Show a sample weapon
                if weapon_data['lines']:
                    sample = weapon_data['lines'][0]
                    logger.info(f"  Sample: {sample.get('name')} = {sample.get('chaosValue')} chaos")
            else:
                logger.error("✗ Failed to fetch weapon data")
                return False
                
            # Test league state
            logger.info("Fetching league state...")
            league_data = await api.get_league_state()
            
            if league_data and 'economyLeagues' in league_data:
                leagues = league_data['economyLeagues']
                logger.info(f"✓ League state: {len(leagues)} leagues available")
                
                for league in leagues[:3]:  # Show first 3
                    logger.info(f"  League: {league.get('displayName')} ({league.get('name')})")
            else:
                logger.error("✗ Failed to fetch league state")
                return False
                
            # Test metrics
            metrics = api.get_metrics_summary()
            if metrics:
                logger.info(f"✓ API metrics: {metrics.get('total_requests', 0)} requests, {metrics.get('success_rate', 0):.1f}% success rate")
            
            return True
            
    except Exception as e:
        logger.error(f"✗ API client test failed: {e}")
        return False

def test_configuration():
    """Test configuration loading"""
    logger.info("Testing configuration...")
    
    try:
        from config.settings import get_settings
        
        settings = get_settings()
        
        logger.info(f"✓ Configuration loaded:")
        logger.info(f"  Default league: {settings.api.default_league}")
        logger.info(f"  Poll interval: {settings.scheduler.poll_interval_minutes} minutes")
        logger.info(f"  Max concurrent: {settings.api.max_concurrent_requests}")
        logger.info(f"  Environment: {'development' if settings.is_development() else 'production'}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Configuration test failed: {e}")
        return False

async def test_validation():
    """Test data validation"""
    logger.info("Testing data validation...")

    try:
        from ingestion.validator import DataValidator, ValidationResult

        validator = DataValidator()

        # Test currency schema
        sample_currency_data = {
            "lines": [
                {
                    "currencyTypeName": "Chaos Orb",
                    "chaosEquivalent": 1.0,
                    "pay": {"value": 1.0},
                    "receive": {"value": 1.0},
                    "count": 100,
                    "listingCount": 50
                }
            ],
            "currencyDetails": [
                {
                    "id": 1,
                    "name": "Chaos Orb",
                    "icon": "chaos.png"
                }
            ]
        }

        # Test validation
        validation_results = await validator.validate_all({
            "Currency": sample_currency_data
        })

        if validation_results and "Currency" in validation_results:
            result = validation_results["Currency"]
            logger.info(f"✓ Validation test: {result.result.value} with confidence {result.confidence_score:.2f}")

            if result.warnings:
                logger.info(f"  Warnings: {len(result.warnings)}")

            return result.result in [ValidationResult.VALID, ValidationResult.SUSPICIOUS]
        else:
            logger.error("✗ Validation test failed: no results")
            return False

    except Exception as e:
        logger.error(f"✗ Validation test failed: {e}")
        return False

async def main():
    """Run all basic tests"""
    logger.info("=" * 60)
    logger.info("PoE Dashboard Basic Functionality Test")
    logger.info("=" * 60)
    
    tests = [
        ("Configuration", test_configuration),
        ("API Client", test_api_client),
        ("Data Validation", test_validation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n--- Testing {test_name} ---")
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
                
            results.append((test_name, result))
            
        except Exception as e:
            logger.error(f"✗ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("Test Results Summary")
    logger.info("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        logger.info(f"{status}: {test_name}")
        if result:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        logger.info("🎉 All basic tests passed! The system appears to be working correctly.")
        return 0
    else:
        logger.error("❌ Some tests failed. Please check the logs above for details.")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Test suite crashed: {e}")
        sys.exit(1)
