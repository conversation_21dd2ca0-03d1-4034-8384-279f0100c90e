use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, sqlx::FromRow)]
pub struct League {
    pub id: i64,
    pub name: String,
    pub display_name: String,
    pub is_active: bool,
    pub first_seen: DateTime<Utc>,
    pub last_seen: DateTime<Utc>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, sqlx::FromRow)]
pub struct Item {
    pub id: i64,
    pub details_id: String,
    pub name: String,
    pub type_line: Option<String>,
    pub base_type: Option<String>,
    pub category: String,
    pub subcategory: Option<String>,
    pub icon_url: Option<String>,
    pub league_id: i64,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, sqlx::FromRow)]
pub struct Price {
    pub id: i64,
    pub item_id: i64,
    pub league_id: i64,
    pub chaos_value: Option<f64>,
    pub divine_value: Option<f64>,
    pub listing_count: i32,
    pub volatility: Option<f64>,
    pub momentum_7d: Option<f64>,
    pub liquidity_score: Option<f64>,
    pub confidence_score: f64,
    pub is_outlier: bool,
    pub snapshot_time: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriceQuery {
    pub item_name: String,
    pub league: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriceResult {
    pub item_name: String,
    pub chaos_value: Option<f64>,
    pub divine_value: Option<f64>,
    pub confidence: f64,
    pub last_updated: String,
    pub trend_7d: Option<f64>,
    pub liquidity: String,
    pub listing_count: i32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FlipFinderQuery {
    pub budget_chaos: f64,
    pub min_profit_pct: f64,
    pub max_risk_level: RiskLevel,
    pub league: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FlipOpportunity {
    pub item_name: String,
    pub buy_price: f64,
    pub sell_price: f64,
    pub profit_chaos: f64,
    pub profit_pct: f64,
    pub risk_level: RiskLevel,
    pub confidence: f64,
    pub liquidity_score: f64,
    pub time_to_sell_estimate: String,
    pub listing_count: i32,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, PartialOrd)]
pub enum RiskLevel {
    Low,
    Medium,
    High,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PortfolioItem {
    pub id: Option<i64>,
    pub item_name: String,
    pub quantity: i32,
    pub purchase_price: f64,
    pub current_value: Option<f64>,
    pub profit_loss: Option<f64>,
    pub profit_loss_pct: Option<f64>,
    pub league: String,
    pub added_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MarketSummary {
    pub total_items: i64,
    pub active_leagues: i64,
    pub last_update: DateTime<Utc>,
    pub top_movers: Vec<TopMover>,
    pub currency_rates: CurrencyRates,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TopMover {
    pub item_name: String,
    pub change_pct: f64,
    pub current_price: f64,
    pub volume: i32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CurrencyRates {
    pub chaos_to_divine: f64,
    pub divine_to_chaos: f64,
    pub exalt_to_chaos: Option<f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppInfo {
    pub version: String,
    pub startup_time_ms: u64,
    pub uptime_seconds: u64,
    pub database_size_mb: f64,
    pub cache_size: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    pub avg_query_time_ms: f64,
    pub cache_hit_rate: f64,
    pub memory_usage_mb: f64,
    pub cpu_usage_percent: f64,
    pub active_connections: u32,
    pub last_updated: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SyncStatus {
    pub is_syncing: bool,
    pub last_sync: Option<DateTime<Utc>>,
    pub last_error: Option<String>,
    pub items_synced: u32,
    pub sync_duration_ms: u64,
    pub next_sync_eta: Option<DateTime<Utc>>,
}

// API response types from poe.ninja
#[derive(Debug, Clone, Deserialize)]
pub struct PoeNinjaResponse {
    pub lines: Vec<PoeNinjaItem>,
    #[serde(rename = "currencyDetails")]
    pub currency_details: Option<Vec<CurrencyDetail>>,
}

#[derive(Debug, Clone, Deserialize)]
pub struct PoeNinjaItem {
    // Currency fields
    #[serde(rename = "currencyTypeName")]
    pub currency_type_name: Option<String>,
    #[serde(rename = "chaosEquivalent")]
    pub chaos_equivalent: Option<f64>,
    #[serde(rename = "divineEquivalent")]
    pub divine_equivalent: Option<f64>,

    // Item fields
    pub id: Option<i32>,
    pub name: Option<String>,
    #[serde(rename = "typeLine")]
    pub type_line: Option<String>,
    #[serde(rename = "baseType")]
    pub base_type: Option<String>,
    pub icon: Option<String>,
    #[serde(rename = "detailsId")]
    pub details_id: Option<String>,

    // Price fields
    #[serde(rename = "chaosValue")]
    pub chaos_value: Option<f64>,
    #[serde(rename = "exaltedValue")]
    pub exalted_value: Option<f64>,
    #[serde(rename = "divineValue")]
    pub divine_value: Option<f64>,

    // Listing information
    #[serde(rename = "listingCount")]
    pub listing_count: Option<i32>,
    pub count: Option<i32>,

    // Additional fields that might be present
    #[serde(rename = "stackSize")]
    pub stack_size: Option<i32>,
    #[serde(rename = "itemClass")]
    pub item_class: Option<i32>,
    #[serde(rename = "levelRequired")]
    pub level_required: Option<i32>,
    #[serde(rename = "mapTier")]
    pub map_tier: Option<i32>,
    pub variant: Option<String>,
    #[serde(rename = "itemType")]
    pub item_type: Option<String>,
    pub corrupted: Option<bool>,
    #[serde(rename = "gemLevel")]
    pub gem_level: Option<i32>,
    #[serde(rename = "gemQuality")]
    pub gem_quality: Option<i32>,
    pub links: Option<i32>,
}

#[derive(Debug, Clone, Deserialize)]
pub struct CurrencyDetail {
    pub id: i32,
    pub icon: Option<String>,
    pub name: String,
    #[serde(rename = "tradeId")]
    pub trade_id: Option<String>,
}

impl Default for RiskLevel {
    fn default() -> Self {
        RiskLevel::Medium
    }
}
