[package]
name = "poe-profit-ai"
version = "1.0.0"
description = "PoE Profit AI - Stand-Alone Desktop Edition"
authors = ["PoE Profit AI Team"]
license = "MIT"
repository = "https://github.com/yourusername/poe-profit-ai"
edition = "2021"

[build-dependencies]
tauri-build = { version = "2.0", features = [] }

[dependencies]
# Core Tauri
tauri = { version = "2.0", features = [] }

# Async runtime
tokio = { version = "1.0", features = ["full"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Database
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "sqlite", "chrono", "migrate"] }

# HTTP client
reqwest = { version = "0.11", features = ["json"] }

# Error handling
anyhow = "1.0"

# Date/time
chrono = { version = "0.4", features = ["serde"] }

# Rate limiting
governor = "0.6"

# Fuzzy matching
fuzzy-matcher = "0.3"

# Logging
log = "0.4"
env_logger = "0.10"

# Statistics
statrs = "0.16"

# Caching
moka = { version = "0.12", features = ["future"] }

# Scheduling
tokio-cron-scheduler = "0.9"

# System info
sysinfo = "0.29"

[dev-dependencies]
tempfile = "3.8"
tokio-test = "0.4"

[features]
default = ["custom-protocol"]
custom-protocol = ["tauri/custom-protocol"]

# Optimize for release builds
[profile.release]
panic = "abort"
codegen-units = 1
lto = true
opt-level = "s"  # Optimize for size
strip = true     # Remove debug symbols
