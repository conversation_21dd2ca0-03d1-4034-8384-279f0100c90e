// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use std::sync::Arc;
use std::time::Instant;
use tokio::sync::RwLock;
use tauri::{State, Manager};

mod models;
mod data;
mod commands;
mod cache;
mod scheduler;

use models::*;
use data::{DatabaseManager, PoeNinjaClient, DataValidator};
use cache::CacheManager;
use scheduler::DataSyncScheduler;

// Application state
#[derive(Clone)]
pub struct AppState {
    pub db_manager: Arc<DatabaseManager>,
    pub poe_client: Arc<PoeNinjaClient>,
    pub data_validator: Arc<DataValidator>,
    pub cache_manager: Arc<RwLock<CacheManager>>,
    pub sync_scheduler: Arc<RwLock<Option<DataSyncScheduler>>>,
    pub startup_time: Instant,
}



// Import commands from separate modules
use commands::*;







#[tokio::main]
async fn main() {
    // Initialize logging with debug level for development
    env_logger::Builder::from_default_env()
        .filter_level(log::LevelFilter::Debug)
        .init();

    log::info!("Starting PoE Profit AI application...");
    log::debug!("Debug logging enabled for poe.ninja sync debugging");

    // Initialize application state
    let startup_time = Instant::now();

    // Initialize database - use temp directory for now to avoid permissions issues
    let temp_dir = std::env::temp_dir();
    let db_path = temp_dir.join("poe_profit.db");
    let db_path_str = db_path.to_string_lossy();

    log::debug!("Temp directory: {:?}", temp_dir);
    log::info!("Initializing database at: {}", db_path_str);

    log::debug!("Creating database manager...");
    let db_manager = Arc::new(
        DatabaseManager::new(&db_path_str)
            .await
            .expect("Failed to initialize database")
    );
    log::info!("Database manager created successfully!");

    // Initialize PoE.ninja client
    log::debug!("Creating PoE.ninja client...");
    let poe_client = Arc::new(PoeNinjaClient::new());

    // Create clone for setup closure before moving the original
    let poe_client_for_setup = poe_client.clone();

    // Initialize data validator
    log::debug!("Creating data validator...");
    let data_validator = Arc::new(DataValidator::default());

    // Initialize cache manager
    log::debug!("Creating cache manager...");
    let cache_manager = Arc::new(RwLock::new(CacheManager::new()));

    // Create placeholder for sync scheduler (will be initialized in setup)
    log::debug!("Creating sync scheduler placeholder...");
    let sync_scheduler = Arc::new(RwLock::new(None::<DataSyncScheduler>));

    log::debug!("Creating app state...");
    let app_state = AppState {
        db_manager: db_manager.clone(),
        poe_client, // This moves the original poe_client
        data_validator,
        cache_manager,
        sync_scheduler: sync_scheduler.clone(),
        startup_time,
    };

    log::info!("Starting Tauri application...");
    tauri::Builder::default()
        .manage(app_state)
        .setup(move |app| {
            // Initialize sync scheduler with proper app handle
            let app_handle = app.handle().clone();
            let db_manager_clone = db_manager.clone();
            let poe_client_clone = poe_client_for_setup.clone();
            let sync_scheduler_clone = sync_scheduler.clone();

            tauri::async_runtime::spawn(async move {
                match DataSyncScheduler::new(db_manager_clone, poe_client_clone, app_handle).await {
                    Ok(mut scheduler) => {
                        log::info!("Starting background sync scheduler...");
                        if let Err(e) = scheduler.start().await {
                            log::error!("Failed to start sync scheduler: {}", e);
                        }
                        *sync_scheduler_clone.write().await = Some(scheduler);
                    }
                    Err(e) => {
                        log::error!("Failed to initialize sync scheduler: {}", e);
                    }
                }
            });
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            // Flip commands
            find_flip_opportunities,
            get_top_flip_opportunities,

            // Market commands
            get_market_summary,
            get_league_list,

            // Portfolio commands
            get_portfolio_items,
            add_portfolio_item,
            remove_portfolio_item,
            get_portfolio_total_value,

            // Price commands
            get_price_history,

            // System commands
            get_app_info,
            get_sync_status,
            force_data_sync,
            test_database_performance,

        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}


