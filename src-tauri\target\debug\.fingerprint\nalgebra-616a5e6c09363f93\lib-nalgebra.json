{"rustc": 10895048813736897673, "features": "[\"default\", \"macros\", \"matrixmultiply\", \"nalgebra-macros\", \"rand\", \"rand-no-std\", \"rand-package\", \"rand_distr\", \"std\"]", "declared_features": "[\"abomonation\", \"abomonation-serialize\", \"alga\", \"alloc\", \"arbitrary\", \"bytemuck\", \"compare\", \"convert-bytemuck\", \"convert-glam013\", \"convert-glam014\", \"convert-glam015\", \"convert-glam016\", \"convert-glam017\", \"convert-mint\", \"debug\", \"default\", \"glam013\", \"glam014\", \"glam015\", \"glam016\", \"glam017\", \"io\", \"libm\", \"libm-force\", \"macros\", \"matrixcompare-core\", \"matrixmultiply\", \"mint\", \"nalgebra-macros\", \"pest\", \"pest_derive\", \"proptest\", \"proptest-support\", \"quickcheck\", \"rand\", \"rand-no-std\", \"rand-package\", \"rand_distr\", \"rkyv\", \"rkyv-serialize\", \"rkyv-serialize-no-std\", \"serde\", \"serde-serialize\", \"serde-serialize-no-std\", \"slow-tests\", \"sparse\", \"std\"]", "target": 572955357253318494, "profile": 2241668132362809309, "path": 3745424162058657364, "deps": [[2819946551904607991, "num_rational", false, 5007528431143306475], [5157631553186200874, "num_traits", false, 5283441251503316404], [8433938214054973130, "simba", false, 6511078259265825260], [9196727883430091646, "rand_distr", false, 10397629759137651059], [12319020793864570031, "num_complex", false, 10934863629982645839], [13208667028893622512, "rand_package", false, 16836568625260154127], [15677050387741058262, "approx", false, 17666655560267368151], [15826188163127377936, "matrixmultiply", false, 14895420616377781698], [17001665395952474378, "typenum", false, 4685483356130637478], [17514367198935401919, "nalgebra_macros", false, 10656747589698065003]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\nalgebra-616a5e6c09363f93\\dep-lib-nalgebra", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}