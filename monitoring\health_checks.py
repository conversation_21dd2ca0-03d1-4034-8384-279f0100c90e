"""
Health Monitoring Component
==========================
Implements comprehensive health checks and monitoring for the data pipeline
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

from database.connection import DatabaseConnection

logger = logging.getLogger(__name__)

class HealthStatus(Enum):
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"

@dataclass
class HealthCheck:
    """Individual health check result"""
    name: str
    status: HealthStatus
    message: str
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

class HealthChecker:
    """
    Comprehensive health monitoring system that checks:
    - Database connectivity and performance
    - Data freshness and quality
    - API fetch success rates
    - System resource usage
    - Data pipeline integrity
    """
    
    def __init__(self, db_connection: DatabaseConnection):
        self.db_connection = db_connection
        
        # Health check thresholds from blueprint
        self.max_data_age_minutes = 90  # Stale data threshold
        self.min_success_rate = 99.0    # Minimum fetch success rate
        self.max_latency_ms = 120       # Maximum acceptable latency
        self.min_confidence_score = 0.7 # Minimum data confidence
        
    async def get_health_status(self) -> Dict[str, Any]:
        """Get overall health status"""
        logger.info("Running comprehensive health checks")
        
        # Run all health checks
        checks = await asyncio.gather(
            self._check_database_health(),
            self._check_data_freshness(),
            self._check_fetch_performance(),
            self._check_data_quality(),
            self._check_pipeline_integrity(),
            return_exceptions=True
        )
        
        # Process results
        health_checks = []
        for check in checks:
            if isinstance(check, Exception):
                health_checks.append(HealthCheck(
                    name="unknown",
                    status=HealthStatus.CRITICAL,
                    message=f"Health check failed: {str(check)}"
                ))
            else:
                health_checks.append(check)
                
        # Determine overall status
        overall_status = self._determine_overall_status(health_checks)
        
        return {
            "overall_status": overall_status.value,
            "timestamp": datetime.now().isoformat(),
            "checks": [
                {
                    "name": check.name,
                    "status": check.status.value,
                    "message": check.message,
                    "details": check.details,
                    "timestamp": check.timestamp.isoformat()
                }
                for check in health_checks
            ]
        }
        
    async def _check_database_health(self) -> HealthCheck:
        """Check database connectivity and performance"""
        try:
            start_time = datetime.now()
            
            # Test basic connectivity
            is_healthy = await self.db_connection.health_check()
            if not is_healthy:
                return HealthCheck(
                    name="database_connectivity",
                    status=HealthStatus.CRITICAL,
                    message="Database connection failed"
                )
                
            # Test query performance
            query_time = (datetime.now() - start_time).total_seconds() * 1000
            
            # Check connection pool status
            pool_info = {}
            if hasattr(self.db_connection, 'pool') and self.db_connection.pool:
                pool_info = {
                    "size": self.db_connection.pool.get_size(),
                    "min_size": self.db_connection.pool.get_min_size(),
                    "max_size": self.db_connection.pool.get_max_size(),
                    "idle_size": self.db_connection.pool.get_idle_size()
                }
                
            if query_time > 1000:  # > 1 second is concerning
                status = HealthStatus.WARNING
                message = f"Database query slow: {query_time:.1f}ms"
            else:
                status = HealthStatus.HEALTHY
                message = f"Database healthy: {query_time:.1f}ms response time"
                
            return HealthCheck(
                name="database_health",
                status=status,
                message=message,
                details={
                    "query_time_ms": query_time,
                    "pool_info": pool_info
                }
            )
            
        except Exception as e:
            return HealthCheck(
                name="database_health",
                status=HealthStatus.CRITICAL,
                message=f"Database health check failed: {str(e)}"
            )
            
    async def _check_data_freshness(self) -> HealthCheck:
        """Check if data is fresh (not stale)"""
        try:
            # Check most recent data timestamp
            query = """
                SELECT 
                    MAX(snapshot_time) as latest_snapshot,
                    COUNT(*) as total_records
                FROM prices 
                WHERE snapshot_time >= NOW() - INTERVAL '2 hours'
            """
            
            result = await self.db_connection.fetch_one(query)
            
            if not result or not result['latest_snapshot']:
                return HealthCheck(
                    name="data_freshness",
                    status=HealthStatus.CRITICAL,
                    message="No recent data found"
                )
                
            latest_snapshot = result['latest_snapshot']
            age_minutes = (datetime.now() - latest_snapshot).total_seconds() / 60
            total_records = result['total_records']
            
            if age_minutes > self.max_data_age_minutes:
                status = HealthStatus.CRITICAL
                message = f"Data is stale: {age_minutes:.1f} minutes old"
            elif age_minutes > (self.max_data_age_minutes * 0.7):
                status = HealthStatus.WARNING
                message = f"Data aging: {age_minutes:.1f} minutes old"
            else:
                status = HealthStatus.HEALTHY
                message = f"Data fresh: {age_minutes:.1f} minutes old"
                
            return HealthCheck(
                name="data_freshness",
                status=status,
                message=message,
                details={
                    "latest_snapshot": latest_snapshot.isoformat(),
                    "age_minutes": age_minutes,
                    "recent_records": total_records
                }
            )
            
        except Exception as e:
            return HealthCheck(
                name="data_freshness",
                status=HealthStatus.CRITICAL,
                message=f"Data freshness check failed: {str(e)}"
            )
            
    async def _check_fetch_performance(self) -> HealthCheck:
        """Check API fetch performance metrics"""
        try:
            # Check fetch success rate and latency from last hour
            query = """
                SELECT 
                    COUNT(*) as total_requests,
                    COUNT(*) FILTER (WHERE status = 200) as successful_requests,
                    AVG(latency_ms) as avg_latency,
                    MAX(latency_ms) as max_latency,
                    COUNT(DISTINCT endpoint) as unique_endpoints
                FROM stats_fetch 
                WHERE fetch_time >= NOW() - INTERVAL '1 hour'
            """
            
            result = await self.db_connection.fetch_one(query)
            
            if not result or result['total_requests'] == 0:
                return HealthCheck(
                    name="fetch_performance",
                    status=HealthStatus.WARNING,
                    message="No recent fetch attempts"
                )
                
            success_rate = (result['successful_requests'] / result['total_requests']) * 100
            avg_latency = result['avg_latency'] or 0
            max_latency = result['max_latency'] or 0
            
            # Determine status based on success rate and latency
            if success_rate < self.min_success_rate:
                status = HealthStatus.CRITICAL
                message = f"Low success rate: {success_rate:.1f}%"
            elif avg_latency > self.max_latency_ms:
                status = HealthStatus.WARNING
                message = f"High latency: {avg_latency:.1f}ms average"
            else:
                status = HealthStatus.HEALTHY
                message = f"Fetch performance good: {success_rate:.1f}% success, {avg_latency:.1f}ms avg"
                
            return HealthCheck(
                name="fetch_performance",
                status=status,
                message=message,
                details={
                    "success_rate_percent": success_rate,
                    "avg_latency_ms": avg_latency,
                    "max_latency_ms": max_latency,
                    "total_requests": result['total_requests'],
                    "unique_endpoints": result['unique_endpoints']
                }
            )
            
        except Exception as e:
            return HealthCheck(
                name="fetch_performance",
                status=HealthStatus.CRITICAL,
                message=f"Fetch performance check failed: {str(e)}"
            )
            
    async def _check_data_quality(self) -> HealthCheck:
        """Check data quality metrics"""
        try:
            # Check data quality from recent snapshots
            query = """
                SELECT 
                    COUNT(*) as total_records,
                    COUNT(*) FILTER (WHERE confidence_score >= $1) as high_confidence_records,
                    COUNT(*) FILTER (WHERE is_outlier = true) as outlier_records,
                    AVG(confidence_score) as avg_confidence,
                    COUNT(*) FILTER (WHERE listing_count >= 10) as liquid_records
                FROM prices 
                WHERE snapshot_time >= NOW() - INTERVAL '1 hour'
            """
            
            result = await self.db_connection.fetch_one(query, self.min_confidence_score)
            
            if not result or result['total_records'] == 0:
                return HealthCheck(
                    name="data_quality",
                    status=HealthStatus.WARNING,
                    message="No recent data for quality analysis"
                )
                
            total_records = result['total_records']
            high_confidence_rate = (result['high_confidence_records'] / total_records) * 100
            outlier_rate = (result['outlier_records'] / total_records) * 100
            avg_confidence = result['avg_confidence'] or 0
            liquid_rate = (result['liquid_records'] / total_records) * 100
            
            # Determine status based on quality metrics
            if high_confidence_rate < 70:
                status = HealthStatus.WARNING
                message = f"Low confidence data: {high_confidence_rate:.1f}% high confidence"
            elif outlier_rate > 10:
                status = HealthStatus.WARNING
                message = f"High outlier rate: {outlier_rate:.1f}%"
            else:
                status = HealthStatus.HEALTHY
                message = f"Data quality good: {high_confidence_rate:.1f}% high confidence"
                
            return HealthCheck(
                name="data_quality",
                status=status,
                message=message,
                details={
                    "total_records": total_records,
                    "high_confidence_rate_percent": high_confidence_rate,
                    "outlier_rate_percent": outlier_rate,
                    "avg_confidence_score": avg_confidence,
                    "liquid_rate_percent": liquid_rate
                }
            )
            
        except Exception as e:
            return HealthCheck(
                name="data_quality",
                status=HealthStatus.CRITICAL,
                message=f"Data quality check failed: {str(e)}"
            )
            
    async def _check_pipeline_integrity(self) -> HealthCheck:
        """Check overall pipeline integrity"""
        try:
            # Check for processing errors in staging
            query = """
                SELECT 
                    COUNT(*) as total_staging,
                    COUNT(*) FILTER (WHERE processed = false) as unprocessed,
                    COUNT(*) FILTER (WHERE processing_error IS NOT NULL) as error_records
                FROM staging_raw 
                WHERE fetched_at >= NOW() - INTERVAL '2 hours'
            """
            
            result = await self.db_connection.fetch_one(query)
            
            if not result:
                return HealthCheck(
                    name="pipeline_integrity",
                    status=HealthStatus.WARNING,
                    message="No staging data found"
                )
                
            total_staging = result['total_staging']
            unprocessed = result['unprocessed']
            error_records = result['error_records']
            
            if total_staging == 0:
                status = HealthStatus.WARNING
                message = "No recent staging data"
            elif error_records > 0:
                error_rate = (error_records / total_staging) * 100
                if error_rate > 10:
                    status = HealthStatus.CRITICAL
                    message = f"High processing error rate: {error_rate:.1f}%"
                else:
                    status = HealthStatus.WARNING
                    message = f"Some processing errors: {error_records} records"
            elif unprocessed > (total_staging * 0.5):
                status = HealthStatus.WARNING
                message = f"Processing backlog: {unprocessed} unprocessed records"
            else:
                status = HealthStatus.HEALTHY
                message = "Pipeline processing normally"
                
            return HealthCheck(
                name="pipeline_integrity",
                status=status,
                message=message,
                details={
                    "total_staging_records": total_staging,
                    "unprocessed_records": unprocessed,
                    "error_records": error_records
                }
            )
            
        except Exception as e:
            return HealthCheck(
                name="pipeline_integrity",
                status=HealthStatus.CRITICAL,
                message=f"Pipeline integrity check failed: {str(e)}"
            )
            
    def _determine_overall_status(self, health_checks: List[HealthCheck]) -> HealthStatus:
        """Determine overall health status from individual checks"""
        if not health_checks:
            return HealthStatus.UNKNOWN
            
        # If any check is critical, overall is critical
        if any(check.status == HealthStatus.CRITICAL for check in health_checks):
            return HealthStatus.CRITICAL
            
        # If any check is warning, overall is warning
        if any(check.status == HealthStatus.WARNING for check in health_checks):
            return HealthStatus.WARNING
            
        # If all checks are healthy, overall is healthy
        if all(check.status == HealthStatus.HEALTHY for check in health_checks):
            return HealthStatus.HEALTHY
            
        return HealthStatus.UNKNOWN
        
    async def get_detailed_metrics(self) -> Dict[str, Any]:
        """Get detailed metrics for monitoring dashboard"""
        try:
            # Database metrics
            db_metrics = await self._get_database_metrics()
            
            # Fetch metrics
            fetch_metrics = await self._get_fetch_metrics()
            
            # Data metrics
            data_metrics = await self._get_data_metrics()
            
            return {
                "database": db_metrics,
                "fetch": fetch_metrics,
                "data": data_metrics,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get detailed metrics: {e}")
            return {"error": str(e)}
            
    async def _get_database_metrics(self) -> Dict[str, Any]:
        """Get database-specific metrics"""
        try:
            query = """
                SELECT 
                    (SELECT COUNT(*) FROM leagues WHERE is_active = true) as active_leagues,
                    (SELECT COUNT(*) FROM items) as total_items,
                    (SELECT COUNT(*) FROM prices WHERE snapshot_time >= NOW() - INTERVAL '24 hours') as recent_prices,
                    (SELECT COUNT(*) FROM staging_raw WHERE fetched_at >= NOW() - INTERVAL '24 hours') as recent_staging
            """
            
            result = await self.db_connection.fetch_one(query)
            return dict(result) if result else {}
            
        except Exception as e:
            return {"error": str(e)}
            
    async def _get_fetch_metrics(self) -> Dict[str, Any]:
        """Get fetch-specific metrics"""
        try:
            query = """
                SELECT 
                    endpoint,
                    COUNT(*) as total_requests,
                    COUNT(*) FILTER (WHERE status = 200) as successful_requests,
                    AVG(latency_ms) as avg_latency_ms,
                    MAX(fetch_time) as last_fetch
                FROM stats_fetch 
                WHERE fetch_time >= NOW() - INTERVAL '24 hours'
                GROUP BY endpoint
                ORDER BY endpoint
            """
            
            results = await self.db_connection.fetch_all(query)
            
            metrics_by_endpoint = {}
            for row in results:
                endpoint = row['endpoint']
                success_rate = (row['successful_requests'] / row['total_requests']) * 100
                
                metrics_by_endpoint[endpoint] = {
                    "total_requests": row['total_requests'],
                    "success_rate_percent": round(success_rate, 2),
                    "avg_latency_ms": round(row['avg_latency_ms'], 2) if row['avg_latency_ms'] else 0,
                    "last_fetch": row['last_fetch'].isoformat() if row['last_fetch'] else None
                }
                
            return metrics_by_endpoint
            
        except Exception as e:
            return {"error": str(e)}
            
    async def _get_data_metrics(self) -> Dict[str, Any]:
        """Get data-specific metrics"""
        try:
            query = """
                SELECT 
                    COUNT(*) as total_current_prices,
                    AVG(confidence_score) as avg_confidence,
                    COUNT(*) FILTER (WHERE is_outlier = true) as outliers,
                    COUNT(*) FILTER (WHERE listing_count >= 10) as liquid_items,
                    MAX(snapshot_time) as latest_snapshot
                FROM prices p
                JOIN leagues l ON p.league_id = l.id
                WHERE l.is_active = true
                  AND p.snapshot_time >= NOW() - INTERVAL '2 hours'
            """
            
            result = await self.db_connection.fetch_one(query)
            
            if result:
                return {
                    "total_current_prices": result['total_current_prices'],
                    "avg_confidence_score": round(result['avg_confidence'], 3) if result['avg_confidence'] else 0,
                    "outlier_count": result['outliers'],
                    "liquid_items": result['liquid_items'],
                    "latest_snapshot": result['latest_snapshot'].isoformat() if result['latest_snapshot'] else None
                }
            else:
                return {}
                
        except Exception as e:
            return {"error": str(e)}
