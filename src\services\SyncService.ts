import { listen } from '@tauri-apps/api/event';
import { invoke } from '@tauri-apps/api/core';
import { useAppStore } from '../stores/appStore';

/**
 * Handles data synchronization with the backend.
 */
const HEARTBEAT_MS = 2000;
const WATCHDOG_MS = HEARTBEAT_MS * 3;

class SyncService {
  private static isInitialized = false;
  private static watchdogTimer: NodeJS.Timeout | null = null;

  /**
   * Initializes the sync service by setting up listeners for sync events.
   * This function is guarded to only run once.
   */
  public static init() {
    if (this.isInitialized) {
      return;
    }
    this.isInitialized = true;

    listen('sync-progress', ({ payload }: { payload: { done: number; total: number } }) => {
      this._onProgress(payload);
    });

    listen('sync-completed', () => {
      this._clearWatchdog();
      useAppStore.getState().setSyncStatus('completed', { lastSync: new Date() });
    });

    listen('sync-error', (event) => {
      this._clearWatchdog();
      const errorMessage = (event.payload as any)?.error || 'An unknown error occurred during sync.';
      useAppStore.getState().setSyncStatus('error', { errorMsg: errorMessage });
    });
  }

  private static _onProgress({ done, total }: { done: number; total: number }) {
    const { syncState, setSyncStatus, updateProgress } = useAppStore.getState();
    const pct = total > 0 ? Math.floor((done / total) * 100) : NaN;
    updateProgress(pct, Date.now());
    if (syncState !== 'running') {
      setSyncStatus('running');
    }
  }

  /**
   * Triggers a data synchronization if one is not already in progress.
   * Sets a watchdog timer to prevent the sync from hanging indefinitely.
   */
  public static async forceSync() {
    const { syncState, setSyncStatus, updateProgress } = useAppStore.getState();

    if (syncState === 'running') {
      return;
    }

    updateProgress(0, Date.now());
    setSyncStatus('running');

    this._clearWatchdog();
    this.watchdogTimer = setInterval(() => this._watchdog(), WATCHDOG_MS);

    try {
      await invoke('force_data_sync');
    } catch (error) {
      this._clearWatchdog();
      const errorMessage = error instanceof Error ? error.message : String(error);
      setSyncStatus('error', { errorMsg: `Failed to invoke sync: ${errorMessage}` });
    }
  }

  private static _watchdog() {
    const { syncState, lastBeat, setSyncStatus } = useAppStore.getState();
    if (syncState === 'running' && Date.now() - lastBeat > WATCHDOG_MS) {
      setSyncStatus('error-timeout');
      this._clearWatchdog();
    }
  }

  /**
   * Clears the watchdog timer.
   */
  private static _clearWatchdog() {
    if (this.watchdogTimer) {
      clearInterval(this.watchdogTimer);
      this.watchdogTimer = null;
    }
  }
}

export default SyncService;