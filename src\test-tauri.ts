// Simple test to verify Tauri API import works
import { invoke } from '@tauri-apps/api/core';

console.log('Tauri API imported successfully');

export const testInvoke = async () => {
  try {
    const result = await invoke('get_app_info');
    console.log('<PERSON><PERSON> invoke test successful:', result);
    return result;
  } catch (error) {
    console.error('<PERSON><PERSON> invoke test failed:', error);
    return null;
  }
};
